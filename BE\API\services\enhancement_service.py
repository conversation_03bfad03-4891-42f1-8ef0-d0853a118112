"""
AI Enhancement Service

This service integrates the existing AI text enhancer with the API,
providing high-level enhancement operations for chapter content.
"""

import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# Add the AITextEnhancer directory to Python path
# In Docker, AITextEnhancer is at /app/AITextEnhancer
# In development, it's at ../AITextEnhancer relative to API directory
import os
if os.path.exists("/app/AITextEnhancer"):
    enhancer_path = Path("/app/AITextEnhancer")
else:
    enhancer_path = Path(__file__).parent.parent.parent / "AITextEnhancer"
sys.path.insert(0, str(enhancer_path))

# Import AI enhancer module dynamically
import importlib.util

spec = importlib.util.spec_from_file_location("ai_enhancer", enhancer_path / "ai_enhancer.py")
ai_enhancer_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(ai_enhancer_module)
GeminiTextEnhancer = ai_enhancer_module.GeminiTextEnhancer

from API.utils.logging_config import LoggerMixin
from API.middleware.error_handling import AIEnhancementError
from API.core.config import get_settings


import logging

class EnhancementService(LoggerMixin):
    _logger = logging.getLogger(__name__)
    """Service for AI-powered content enhancement"""

    _enhancer = None
    _initialized = False

    @classmethod
    async def initialize(cls):
        """Initialize AI enhancer as a singleton"""
        if cls._initialized:
            return

        try:
            cls._logger.info("Initializing AI enhancement service...")
            settings = get_settings()

            # Initialize the real GeminiTextEnhancer
            cls._enhancer = GeminiTextEnhancer(
                api_key=settings.google_ai_api_key,
                model_name=settings.ai_model_name
            )



            cls._initialized = True
            cls._logger.info("✅ AI enhancement service initialized successfully")

        except Exception as e:
            cls._logger.error(f"Failed to initialize AI enhancement service: {e}")
            raise AIEnhancementError(f"Enhancement service initialization failed: {e}")

    @classmethod
    async def cleanup(cls):
        """Cleanup AI enhancer resources"""
        cls._enhancer = None
        cls._initialized = False
        cls._logger.info("✅ AI enhancement service cleaned up")

    def _get_enhancer(self):
        """Get the initialized enhancer instance"""
        if not self._initialized or not self._enhancer:
            raise AIEnhancementError("Enhancement service is not initialized. Call initialize() first.")
        return self._enhancer
    
    async def enhance_chapter(
        self,
        chapter_id: str,
        chapter_title: str,
        content: str
    ) -> Dict[str, Any]:
        """
        Enhance a single chapter's content (Mock implementation)

        Args:
            chapter_id: Unique identifier for the chapter
            chapter_title: Title of the chapter
            content: Original content to enhance

        Returns:
            Dictionary containing enhancement results
        """
        enhancer = self._get_enhancer()

        try:
            self.log_info(f"Mock enhancing chapter: {chapter_title[:50]}...")
            start_time = time.time()

            # Validate input
            if not content or content.strip() == "":
                raise AIEnhancementError("No content provided for enhancement")

            # Mock enhancement - just add some improvements
            # Use the real enhancer
            enhanced_content = await enhancer.enhance_text(
                text=content,
                prompt_type="vietnamese_story_chapter",
                context=f"Chapter '{chapter_title}' of a story."
            )

            # Calculate duration
            duration = time.time() - start_time

            # Calculate metrics
            original_length = len(content)
            enhanced_length = len(enhanced_content)

            # Mock similarity score
            # In a real scenario, these would be calculated or returned by the AI
            similarity_score = await enhancer.calculate_similarity(content, enhanced_content)
            quality_score = await enhancer.rate_quality(enhanced_content)
            improvement_notes = ["Enhanced by Gemini AI"]

            result = {
                "success": True,
                "chapter_id": chapter_id,
                "enhanced_content": enhanced_content,
                "original_length": original_length,
                "enhanced_length": enhanced_length,
                "similarity_score": similarity_score,
                "quality_score": quality_score,
                "improvement_notes": improvement_notes,
                "duration": duration,
                "model_name": "mock-ai-model",
                "enhanced_at": datetime.utcnow().isoformat()
            }

            self.log_info(f"Successfully mock enhanced chapter: {chapter_title[:50]} "
                        f"({original_length} -> {enhanced_length} chars, "
                        f"quality: {quality_score:.2f})")

            return result

        except Exception as e:
            duration = time.time() - start_time if 'start_time' in locals() else 0
            self.log_error(f"Error mock enhancing chapter {chapter_title}: {e}")

            return {
                "success": False,
                "chapter_id": chapter_id,
                "error": str(e),
                "duration": duration
            }
    
    async def enhance_multiple_chapters(
        self,
        chapters: List[Dict[str, Any]],
        batch_size: int = 1
    ) -> List[Dict[str, Any]]:
        """
        Enhance multiple chapters with batch processing
        
        Args:
            chapters: List of chapter dictionaries with id, title, and content
            batch_size: Number of chapters to process in parallel (keep at 1 for rate limits)
            
        Returns:
            List of enhancement results
        """
        enhancer = self._get_enhancer()
        
        try:
            self.log_info(f"Starting batch enhancement of {len(chapters)} chapters")
            
            results = []
            
            # Process chapters sequentially to respect rate limits
            for i, chapter in enumerate(chapters):
                self.log_info(f"Processing chapter {i+1}/{len(chapters)}: {chapter.get('title', 'Unknown')[:50]}")
                
                result = await self.enhance_chapter(
                    chapter_id=chapter["id"],
                    chapter_title=chapter["title"],
                    content=chapter["content"]
                )
                
                results.append(result)
                
                # Add delay between chapters to respect rate limits
                if i < len(chapters) - 1:  # Don't delay after the last chapter
                    await self._rate_limit_delay()
            
            success_count = sum(1 for r in results if r["success"])
            failed_count = len(results) - success_count
            
            self.log_info(f"Batch enhancement completed: {success_count} success, {failed_count} failed")
            
            return results
            
        except Exception as e:
            self.log_error(f"Error in batch chapter enhancement: {e}")
            raise AIEnhancementError(f"Batch enhancement failed: {e}")
    
    async def test_connection(self) -> str:
        """Test AI enhancement service connection (Mock)"""
        try:
            enhancer = self._get_enhancer()

            if self._initialized and self._enhancer:
                return await self._enhancer.test_connection()
            else:
                return "Mock connection failed"

        except Exception as e:
            self.log_error(f"Mock connection test failed: {e}")
            return f"Mock connection failed: {e}"
    
    async def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get enhancement service statistics"""
        stats = {
            "initialized": self._initialized,
            "model_name": self.settings.ai_model_name,
            "rate_limit_delay": self.settings.ai_rate_limit_delay,
            "max_retries": self.settings.ai_max_retries,
            "max_content_length": self.settings.ai_max_content_length
        }
        
        if self.enhancer:
            stats.update({
                "available": self.enhancer.is_available(),
                "status": self.enhancer.get_status()
            })
        
        return stats
    
    def _calculate_similarity(self, original: str, enhanced: str) -> float:
        """Calculate similarity score between original and enhanced content"""
        try:
            # Simple word-based similarity
            original_words = set(original.lower().split())
            enhanced_words = set(enhanced.lower().split())
            
            if not original_words:
                return 0.0
            
            intersection = original_words.intersection(enhanced_words)
            union = original_words.union(enhanced_words)
            
            return len(intersection) / len(union) if union else 0.0
            
        except Exception:
            return 0.0
    
    def _generate_improvement_notes(
        self,
        original: str,
        enhanced: str,
        original_length: int,
        enhanced_length: int
    ) -> List[str]:
        """Generate notes about improvements made"""
        notes = []
        
        # Length comparison
        length_diff = enhanced_length - original_length
        if length_diff > 0:
            notes.append(f"Content expanded by {length_diff} characters")
        elif length_diff < 0:
            notes.append(f"Content condensed by {abs(length_diff)} characters")
        else:
            notes.append("Content length maintained")
        
        # Word count comparison
        original_words = len(original.split())
        enhanced_words = len(enhanced.split())
        word_diff = enhanced_words - original_words
        
        if word_diff > 0:
            notes.append(f"Added {word_diff} words for clarity")
        elif word_diff < 0:
            notes.append(f"Removed {abs(word_diff)} redundant words")
        
        # Basic quality indicators
        if "..." in original and "..." not in enhanced:
            notes.append("Removed ellipsis for better flow")
        
        if enhanced.count(".") > original.count("."):
            notes.append("Improved sentence structure")
        
        return notes
    
    def _calculate_quality_score(
        self,
        original: str,
        enhanced: str,
        similarity_score: float
    ) -> float:
        """Calculate quality score for the enhancement"""
        try:
            score = 0.0
            
            # Base score from similarity (should be high but not too high)
            if 0.7 <= similarity_score <= 0.9:
                score += 0.4
            elif similarity_score > 0.9:
                score += 0.2  # Too similar might mean no improvement
            else:
                score += 0.1  # Too different might be problematic
            
            # Length improvement score
            length_ratio = len(enhanced) / len(original) if original else 0
            if 0.9 <= length_ratio <= 1.3:  # Reasonable length change
                score += 0.3
            else:
                score += 0.1
            
            # Sentence structure score (more sentences might indicate better structure)
            original_sentences = original.count(".")
            enhanced_sentences = enhanced.count(".")
            if enhanced_sentences >= original_sentences:
                score += 0.2
            
            # Vietnamese text quality indicators
            if "," in enhanced and enhanced.count(",") > original.count(","):
                score += 0.1  # Better punctuation
            
            return min(1.0, score)  # Cap at 1.0
            
        except Exception:
            return 0.5  # Default score if calculation fails
    
    async def _rate_limit_delay(self):
        """Add delay for rate limiting"""
        import asyncio
        await asyncio.sleep(self.settings.ai_rate_limit_delay)


# ============================================================================
# Service Factory
# ============================================================================

_enhancement_service_instance: Optional[EnhancementService] = None


def get_enhancement_service() -> EnhancementService:
    """Get singleton enhancement service instance"""
    global _enhancement_service_instance
    
    if _enhancement_service_instance is None:
        _enhancement_service_instance = EnhancementService()
    
    return _enhancement_service_instance


async def cleanup_enhancement_service():
    """Cleanup enhancement service on application shutdown"""
    global _enhancement_service_instance
    
    if _enhancement_service_instance:
        # Clear cache if available
        if _enhancement_service_instance.enhancer:
            _enhancement_service_instance.enhancer.clear_cache()
        _enhancement_service_instance = None
