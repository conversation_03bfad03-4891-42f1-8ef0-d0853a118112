"""
Data Retrieval API Endpoints

This module provides endpoints for retrieving story and chapter data
with pagination, filtering, and search capabilities.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from API.models.schemas import (
    StoryListRequest, StoryListResponse, StoryListItem,
    StoryDetailsResponse, ChapterListRequest, ChapterListResponse,
    ChapterListItem, ChapterContentResponse, PaginationInfo,
    SearchRequest, ContentComparisonRequest, ContentComparisonResponse, APIResponse
)
from API.models.database import StoryStatus, ScrapingStatus, EnhancementStatus
from API.utils.database import story_service, chapter_service
from API.middleware.error_handling import DatabaseError, create_not_found_response
from API.utils.logging_config import LoggerMixin

router = APIRouter()


class DataController(LoggerMixin):
    """Controller for data retrieval, search, and content operations"""
    
    async def get_story_list(self, request: StoryListRequest) -> StoryListResponse:
        """Get paginated list of stories with filtering"""
        try:
            self.log_info(f"Getting story list - page: {request.page}, size: {request.page_size}")
            
            # Search stories with filters
            stories, total_count = await story_service.search_stories(
                query=request.search,
                author=request.author,
                status=request.status.value if request.status else None,
                genre=request.genre,
                scraping_status=request.scraping_status.value if request.scraping_status else None,
                page=request.page,
                page_size=request.page_size,
                sort_by=request.sort_by,
                sort_order=request.sort_order
            )
            
            # Convert to response format
            story_items = []
            for story in stories:
                item = StoryListItem(
                    id=str(story["_id"]),
                    title=story["title"],
                    author=story.get("metadata", {}).get("author"),
                    status=StoryStatus(story.get("metadata", {}).get("status", "unknown")),
                    total_chapters=story.get("metadata", {}).get("total_chapters"),
                    total_chapters_scraped=story.get("total_chapters_scraped", 0),
                    total_chapters_enhanced=story.get("total_chapters_enhanced", 0),
                    enhancement_progress=story.get("enhancement_progress", 0.0),
                    cover_image_url=story.get("metadata", {}).get("cover_image_url"),
                    created_at=story["created_at"],
                    updated_at=story["updated_at"]
                )
                story_items.append(item)
            
            # Calculate pagination info
            total_pages = (total_count + request.page_size - 1) // request.page_size
            pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total_items=total_count,
                total_pages=total_pages,
                has_next=request.page < total_pages,
                has_previous=request.page > 1
            )
            
            return StoryListResponse(
                success=True,
                message=f"Retrieved {len(story_items)} stories",
                pagination=pagination,
                data=story_items
            )
            
        except Exception as e:
            self.log_error(f"Error getting story list: {e}")
            raise DatabaseError(f"Failed to retrieve story list: {e}")

    async def compare_content(self, request: ContentComparisonRequest) -> ContentComparisonResponse:
        """Compare original and enhanced content for a chapter"""
        try:
            self.log_info(f"Comparing content for chapter: {request.chapter_id}")
            
            # Get chapter
            chapter = await chapter_service.find_by_id(request.chapter_id)
            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found")
            
            original_content = chapter.get("original_content", "")
            enhanced_content = chapter.get("enhanced_content", "")
            
            if not enhanced_content:
                return ContentComparisonResponse(
                    success=False,
                    message="Chapter has not been enhanced yet",
                    chapter_id=request.chapter_id,
                    chapter_title=chapter["title"],
                    original_length=len(original_content),
                    enhanced_length=0
                )
            
            # Calculate metrics
            original_length = len(original_content)
            enhanced_length = len(enhanced_content)
            
            # Get enhancement metadata
            enhancement_metadata = chapter.get("enhancement_metadata", {})
            similarity_score = enhancement_metadata.get("similarity_score")
            improvement_notes = enhancement_metadata.get("improvement_notes", [])
            
            # Generate comparison data based on type
            comparison_data = {}
            
            if request.comparison_type == "side_by_side":
                comparison_data = {
                    "original": original_content,
                    "enhanced": enhanced_content
                }
            
            elif request.comparison_type == "diff":
                comparison_data = self._generate_diff(original_content, enhanced_content)
            
            elif request.comparison_type == "statistics":
                comparison_data = self._generate_statistics(original_content, enhanced_content)
            
            return ContentComparisonResponse(
                success=True,
                message="Content comparison generated successfully",
                chapter_id=request.chapter_id,
                chapter_title=chapter["title"],
                original_length=original_length,
                enhanced_length=enhanced_length,
                similarity_score=similarity_score,
                improvement_notes=improvement_notes,
                comparison_data=comparison_data
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error comparing content: {e}")
            raise DatabaseError(f"Content comparison failed: {e}")

    async def get_search_suggestions(self, query: str, limit: int = 10) -> List[str]:
        """Get search suggestions based on partial query"""
        try:
            self.log_info(f"Getting search suggestions for: {query}")
            
            suggestions = []
            
            # Get title suggestions
            title_pipeline = [
                {
                    "$match": {
                        "title": {"$regex": f"^{query}", "$options": "i"}
                    }
                },
                {
                    "$project": {"title": 1}
                },
                {
                    "$limit": limit // 2
                }
            ]
            
            title_results = await story_service.aggregate(title_pipeline)
            suggestions.extend([story["title"] for story in title_results])
            
            # Get author suggestions
            author_pipeline = [
                {
                    "$match": {
                        "metadata.author": {"$regex": f"^{query}", "$options": "i"}
                    }
                },
                {
                    "$group": {"_id": "$metadata.author"}
                },
                {
                    "$limit": limit // 2
                }
            ]
            
            author_results = await story_service.aggregate(author_pipeline)
            suggestions.extend([result["_id"] for result in author_results if result["_id"]])
            
            # Remove duplicates and limit
            suggestions = list(set(suggestions))[:limit]
            
            return suggestions
            
        except Exception as e:
            self.log_error(f"Error getting search suggestions: {e}")
            return []

    def _generate_diff(self, original: str, enhanced: str) -> Dict[str, Any]:
        """Generate diff between original and enhanced content"""
        try:
            import difflib
            
            original_lines = original.splitlines()
            enhanced_lines = enhanced.splitlines()
            
            diff = list(difflib.unified_diff(
                original_lines,
                enhanced_lines,
                fromfile="original",
                tofile="enhanced",
                lineterm=""
            ))
            
            return {
                "diff_lines": diff,
                "changes_count": len([line for line in diff if line.startswith(('+', '-'))])
            }
            
        except Exception as e:
            self.log_error(f"Error generating diff: {e}")
            return {"error": "Failed to generate diff"}

    def _generate_statistics(self, original: str, enhanced: str) -> Dict[str, Any]:
        """Generate statistics comparison"""
        try:
            original_words = original.split()
            enhanced_words = enhanced.split()
            
            original_sentences = original.count('.')
            enhanced_sentences = enhanced.count('.')
            
            original_paragraphs = len([p for p in original.split('\n\n') if p.strip()])
            enhanced_paragraphs = len([p for p in enhanced.split('\n\n') if p.strip()])
            
            return {
                "word_count": {
                    "original": len(original_words),
                    "enhanced": len(enhanced_words),
                    "change": len(enhanced_words) - len(original_words)
                },
                "sentence_count": {
                    "original": original_sentences,
                    "enhanced": enhanced_sentences,
                    "change": enhanced_sentences - original_sentences
                },
                "paragraph_count": {
                    "original": original_paragraphs,
                    "enhanced": enhanced_paragraphs,
                    "change": enhanced_paragraphs - original_paragraphs
                },
                "character_count": {
                    "original": len(original),
                    "enhanced": len(enhanced),
                    "change": len(enhanced) - len(original)
                }
            }
            
        except Exception as e:
            self.log_error(f"Error generating statistics: {e}")
            return {"error": "Failed to generate statistics"}

    async def get_story_by_url(self, story_url: str) -> StoryDetailsResponse:
        """Get story details by URL with comprehensive metadata"""
        try:
            self.log_info(f"Retrieving story details by URL: {story_url}")

            # Find story by URL
            story = await story_service.find_by_url(story_url)
            if not story:
                raise HTTPException(status_code=404, detail="Story not found with the provided URL")

            # Use the existing get_story_details method
            return await self.get_story_details(str(story["_id"]))

        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error retrieving story by URL: {e}")
            raise DatabaseError(f"Failed to retrieve story by URL: {e}")

    
    async def get_story_details(self, story_id: str) -> StoryDetailsResponse:
        """Get detailed information about a specific story"""
        try:
            self.log_info(f"Getting story details for: {story_id}")
            
            # Get story from database
            story = await story_service.find_by_id(story_id)
            if not story:
                raise HTTPException(status_code=404, detail="Story not found")
            
            # Build response
            return StoryDetailsResponse(
                success=True,
                message="Story details retrieved successfully",
                id=str(story["_id"]),
                title=story["title"],
                url=story["url"],
                author=story.get("metadata", {}).get("author"),
                description=story.get("metadata", {}).get("description"),
                genres=story.get("metadata", {}).get("genres", []),
                tags=story.get("metadata", {}).get("tags", []),
                status=StoryStatus(story.get("metadata", {}).get("status", "unknown")),
                cover_image_url=story.get("metadata", {}).get("cover_image_url"),
                total_chapters=story.get("metadata", {}).get("total_chapters"),
                total_chapters_scraped=story.get("total_chapters_scraped", 0),
                total_chapters_enhanced=story.get("total_chapters_enhanced", 0),
                enhancement_progress=story.get("enhancement_progress", 0.0),
                scraping_status=ScrapingStatus(story.get("scraping_status", "pending")),
                created_at=story["created_at"],
                updated_at=story["updated_at"],
                last_scraped_at=story.get("last_scraped_at")
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error getting story details: {e}")
            raise DatabaseError(f"Failed to retrieve story details: {e}")
    
    async def get_chapter_list(
        self,
        story_id: str,
        request: ChapterListRequest
    ) -> ChapterListResponse:
        """Get paginated list of chapters for a story"""
        try:
            self.log_info(f"Getting chapter list for story: {story_id}")
            
            # Verify story exists
            story = await story_service.find_by_id(story_id)
            if not story:
                raise HTTPException(status_code=404, detail="Story not found")
            
            # Get chapters with pagination
            chapters, total_count = await chapter_service.find_by_story(
                story_id=story_id,
                enhanced_only=request.enhanced_only,
                scraped_only=request.scraped_only,
                page=request.page,
                page_size=request.page_size
            )
            
            # Convert to response format
            chapter_items = []
            for chapter in chapters:
                item = ChapterListItem(
                    id=str(chapter["_id"]),
                    chapter_number=chapter["chapter_number"],
                    title=chapter["title"],
                    url=chapter["url"],
                    is_scraped=chapter.get("is_scraped", False),
                    is_enhanced=chapter.get("is_enhanced", False),
                    enhancement_status=EnhancementStatus(
                        chapter.get("enhancement_status", "not_enhanced")
                    ),
                    word_count=chapter.get("metadata", {}).get("word_count"),
                    created_at=chapter["created_at"],
                    updated_at=chapter["updated_at"]
                )
                chapter_items.append(item)
            
            # Calculate pagination info
            total_pages = (total_count + request.page_size - 1) // request.page_size
            pagination = PaginationInfo(
                page=request.page,
                page_size=request.page_size,
                total_items=total_count,
                total_pages=total_pages,
                has_next=request.page < total_pages,
                has_previous=request.page > 1
            )
            
            return ChapterListResponse(
                success=True,
                message=f"Retrieved {len(chapter_items)} chapters",
                pagination=pagination,
                data=chapter_items
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error getting chapter list: {e}")
            raise DatabaseError(f"Failed to retrieve chapter list: {e}")
    
    async def get_chapter_content(
        self,
        story_id: str,
        chapter_number: int
    ) -> ChapterContentResponse:
        """Get content for a specific chapter"""
        try:
            self.log_info(f"Getting chapter content: story {story_id}, chapter {chapter_number}")
            
            # Verify story exists
            story = await story_service.find_by_id(story_id)
            if not story:
                raise HTTPException(status_code=404, detail="Story not found")
            
            # Get chapter
            chapter = await chapter_service.find_by_story_and_number(story_id, chapter_number)
            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found")
            
            # Build response
            return ChapterContentResponse(
                success=True,
                message="Chapter content retrieved successfully",
                id=str(chapter["_id"]),
                story_id=str(chapter["story_id"]),
                chapter_number=chapter["chapter_number"],
                title=chapter["title"],
                url=chapter["url"],
                original_content=chapter.get("original_content"),
                enhanced_content=chapter.get("enhanced_content"),
                is_enhanced=chapter.get("is_enhanced", False),
                enhancement_status=EnhancementStatus(
                    chapter.get("enhancement_status", "not_enhanced")
                ),
                word_count=chapter.get("metadata", {}).get("word_count"),
                enhancement_metadata=chapter.get("enhancement_metadata"),
                created_at=chapter["created_at"],
                updated_at=chapter["updated_at"]
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error getting chapter content: {e}")
            raise DatabaseError(f"Failed to retrieve chapter content: {e}")


# ============================================================================
# Router Endpoints
# ============================================================================

controller = DataController()


@router.get("/", response_model=StoryListResponse)
async def get_stories(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    author: Optional[str] = Query(None, description="Filter by author"),
    status: Optional[StoryStatus] = Query(None, description="Filter by story status"),
    genre: Optional[str] = Query(None, description="Filter by genre"),
    scraping_status: Optional[ScrapingStatus] = Query(None, description="Filter by scraping status"),
    sort_by: str = Query("created_at", regex="^(created_at|updated_at|title|total_chapters)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$")
):
    """
    Get paginated list of stories with filtering and search
    
    This endpoint returns a paginated list of all stories with support for
    filtering by various criteria and full-text search.
    """
    request = StoryListRequest(
        page=page,
        page_size=page_size,
        search=search,
        author=author,
        status=status,
        genre=genre,
        scraping_status=scraping_status,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    return await controller.get_story_list(request)


@router.get("/{story_id}", response_model=StoryDetailsResponse)
async def get_story_details(
    story_id: str = Path(..., description="Story ID")
):
    """
    Get detailed information about a specific story
    
    This endpoint returns complete story information including metadata,
    scraping status, and enhancement progress.
    """
    return await controller.get_story_details(story_id)


@router.get("/{story_id}/chapters", response_model=ChapterListResponse)
async def get_story_chapters(
    story_id: str = Path(..., description="Story ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=200, description="Items per page"),
    enhanced_only: bool = Query(False, description="Show only enhanced chapters"),
    scraped_only: bool = Query(False, description="Show only scraped chapters")
):
    """
    Get paginated list of chapters for a story
    
    This endpoint returns chapters for a specific story with optional
    filtering for enhanced or scraped chapters only.
    """
    request = ChapterListRequest(
        story_id=story_id,
        page=page,
        page_size=page_size,
        enhanced_only=enhanced_only,
        scraped_only=scraped_only
    )
    
    return await controller.get_chapter_list(story_id, request)


@router.get("/{story_id}/chapters/{chapter_number}", response_model=ChapterContentResponse)
async def get_chapter_content(
    story_id: str = Path(..., description="Story ID"),
    chapter_number: int = Path(..., ge=1, description="Chapter number")
):
    """
    Get content for a specific chapter
    
    This endpoint returns the full content of a chapter including both
    original and enhanced versions if available.
    """
    return await controller.get_chapter_content(story_id, chapter_number)


@router.get("/chapters/{chapter_id}", response_model=ChapterContentResponse)
async def get_chapter_by_id(
    chapter_id: str = Path(..., description="Chapter ID")
):
    """
    Get chapter content by chapter ID
    
    Alternative endpoint to get chapter content using the chapter ID
    instead of story ID and chapter number.
    """
    try:
        chapter = await chapter_service.find_by_id(chapter_id)
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found")
        
        return await controller.get_chapter_content(
            str(chapter["story_id"]),
            chapter["chapter_number"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise DatabaseError(f"Failed to retrieve chapter: {e}")


@router.post("/advanced-search", response_model=StoryListResponse)
async def advanced_search(request: SearchRequest):
    """
    Perform advanced search with filters
    
    This endpoint provides advanced search capabilities with support for
    multiple search fields, filters, and sorting options.
    """
    # This is a placeholder to integrate the logic from the old search controller.
    # For now, it redirects to the main story list endpoint with basic search.
    request_dict = request.dict()
    return await get_stories(
        page=request_dict.get('page', 1),
        page_size=request_dict.get('page_size', 20),
        search=request_dict.get('query'),
        # Add other filters here as needed
    )


@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum suggestions")
):
    """
    Get search suggestions
    
    This endpoint returns search suggestions based on a partial query
    to help users with autocomplete functionality.
    """
    suggestions = await controller.get_search_suggestions(q, limit)
    
    return {
        "success": True,
        "message": f"Found {len(suggestions)} suggestions",
        "suggestions": suggestions,
        "timestamp": datetime.utcnow().isoformat()
    }


@router.post("/compare-content", response_model=ContentComparisonResponse)
async def compare_content(request: ContentComparisonRequest):
    """
    Compare original and enhanced content

    This endpoint compares the original and enhanced versions of a chapter
    and provides detailed comparison data including differences and statistics.
    """
    return await controller.compare_content(request)


@router.get("/story-by-url", response_model=StoryDetailsResponse)
async def get_story_by_url(
    url: str = Query(..., description="Story URL to search for")
):
    """
    Get comprehensive story details by URL

    This endpoint finds and retrieves complete story information using the story URL:
    - Searches database for story with matching URL
    - Returns comprehensive metadata and scraping information
    - Useful for checking if a story has already been scraped
    """
    return await controller.get_story_by_url(url)
