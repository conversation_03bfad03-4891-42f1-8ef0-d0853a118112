'use client';

import React from 'react';
import Link from 'next/link';
import { Chapter } from '@/types/story';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { cleanChapterTitle } from '@/utils/chapterUtils';

interface ChapterListItemProps {
  storyId: string;
  chapter: Chapter;
  isSelected: boolean;
  selectionMode: 'scraping' | 'enhancement' | null;
  onSelectChapter: (chapterId: string) => void;
}

const getChapterStatusBadge = (chapter: Chapter) => {
  if (chapter.is_enhanced) {
    return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Enhanced</Badge>;
  }
  if (chapter.is_scraped) {
    return <Badge variant="outline" className="bg-green-100 text-green-800">Scraped</Badge>;
  }
  return <Badge variant="destructive" className="bg-gray-100 text-gray-800">Not Scraped</Badge>;
};

export const ChapterListItem: React.FC<ChapterListItemProps> = React.memo(({ 
  storyId, 
  chapter, 
  isSelected, 
  selectionMode, 
  onSelectChapter 
}) => {

  const isSelectable = 
    (selectionMode === 'scraping' && !chapter.is_scraped) ||
    (selectionMode === 'enhancement' && chapter.is_scraped && !chapter.is_enhanced);

  return (
    <div 
      className={`flex items-center p-3 rounded-lg transition-colors ${isSelected ? 'bg-indigo-50 dark:bg-indigo-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'}`}
    >
      {selectionMode && (
        <div className="mr-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onSelectChapter(chapter.id)}
            disabled={!isSelectable}
            aria-label={`Select chapter ${chapter.chapter_number}`}
          />
        </div>
      )}
      <Link href={`/stories/${storyId}/${chapter.chapter_number}`} className="flex-grow flex items-center gap-4 cursor-pointer">
        <span className="font-mono text-sm text-gray-500 w-12 text-right">{chapter.chapter_number}</span>
        <span className="flex-grow font-medium">
          {cleanChapterTitle(chapter.title, chapter.chapter_number)}
        </span>
        <div className="flex items-center gap-2">
          {getChapterStatusBadge(chapter)}
        </div>
      </Link>
    </div>
  );
});

ChapterListItem.displayName = 'ChapterListItem';
