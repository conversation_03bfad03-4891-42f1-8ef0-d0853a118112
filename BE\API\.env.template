# Vietnamese Web Novel API Configuration

# ============================================================================
# Application Settings
# ============================================================================
APP_NAME="Vietnamese Web Novel API"
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# ============================================================================
# Database Configuration
# ============================================================================
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=webtruyen_api

# ============================================================================
# Security Settings
# ============================================================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:8000
ALLOWED_HOSTS=localhost,127.0.0.1,api

# ============================================================================
# AI Enhancement Configuration
# ============================================================================
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
AI_MODEL_NAME=gemini-2.5-flash
AI_RATE_LIMIT_DELAY=0.5
AI_MAX_RETRIES=3
AI_MAX_CONTENT_LENGTH=6000
AI_CHUNK_OVERLAP=200

# ============================================================================
# Scraping Configuration
# ============================================================================
SCRAPING_RATE_LIMIT=10
SCRAPING_BURST_LIMIT=5
MAX_CONCURRENT_SCRAPING=3
MAX_CONCURRENT_ENHANCEMENT=1
SCRAPING_TIMEOUT=30
SCRAPING_DELAY_MIN=1.0
SCRAPING_DELAY_MAX=3.0
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# ============================================================================
# Logging Configuration
# ============================================================================
LOG_LEVEL=INFO
LOG_FILE=/app/logs/api.log
LOG_FORMAT="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"

# ============================================================================
# Cache Configuration
# ============================================================================
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# ============================================================================
# File Storage Configuration
# ============================================================================
STORAGE_PATH=/app/storage
EXPORT_PATH=/app/exports
TEMP_PATH=/app/temp
MAX_EXPORT_FILE_SIZE=100
MAX_UPLOAD_FILE_SIZE=10

# ============================================================================
# Job Management Configuration
# ============================================================================
JOB_CLEANUP_INTERVAL=3600
JOB_RETENTION_DAYS=7
MAX_CONCURRENT_JOBS=5

# ============================================================================
# Monitoring Configuration
# ============================================================================
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=60
