'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Sparkles, SlidersHorizontal, X } from 'lucide-react';
import { ChapterFilter } from '@/hooks/useChapterManagement';
import ChapterStatusFilter from './ChapterStatusFilter';

interface ChapterListHeaderProps {
  stats: {
    total: number;
    scraped: number;
    enhanced: number;
    unscraped: number;
    scrapedNotEnhanced: number;
    selected: number;
  };
  selectionMode: 'scraping' | 'enhancement' | null;
  setSelectionMode: (mode: 'scraping' | 'enhancement' | null) => void;
  onFilterChange: (filter: ChapterFilter) => void;
  filter: ChapterFilter;
}

export const ChapterListHeader: React.FC<ChapterListHeaderProps> = ({ 
  stats, 
  selectionMode, 
  setSelectionMode,
  onFilterChange,
  filter
}) => {
  const handleToggleScrapeMode = () => {
    setSelectionMode(selectionMode === 'scraping' ? null : 'scraping');
  };

  const handleToggleEnhanceMode = () => {
    setSelectionMode(selectionMode === 'enhancement' ? null : 'enhancement');
  };

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50">
      <div className="flex flex-wrap items-center justify-between gap-4">

        <div className="flex items-center gap-2">
          <ChapterStatusFilter 
            filter={filter} 
            onFilterChange={onFilterChange} 
          />
        </div>

        <div className="flex items-center gap-2">
          <Button 
            variant={selectionMode === 'scraping' ? 'default' : 'outline'}
            onClick={handleToggleScrapeMode}
            className="gap-2"
          >
            {selectionMode === 'scraping' ? <X className="h-4 w-4" /> : <Download className="h-4 w-4" />}
            Scrape
          </Button>
          <Button 
            variant={selectionMode === 'enhancement' ? 'default' : 'outline'}
            onClick={handleToggleEnhanceMode}
            className="gap-2"
          >
            {selectionMode === 'enhancement' ? <X className="h-4 w-4" /> : <Sparkles className="h-4 w-4" />}
            Enhance
          </Button>
        </div>
      </div>
    </div>
  );
};
