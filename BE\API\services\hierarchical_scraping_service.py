"""Hierarchical Scraping Service

Implements the new Story → Page → Chapter workflow structure.
This service orchestrates the three-phase scraping process:
1. Story Info Scraping: Extract metadata and generate page URLs
2. Page Processing: Extract chapter URLs from each page
3. Chapter Content Scraping: Process individual chapter content
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from urllib.parse import urljoin, urlparse
import subprocess
import json
import tempfile
from pathlib import Path
import sys

from bson import ObjectId
from fastapi import Request
from API.utils.logging_config import LoggerMixin
from API.middleware.error_handling import ScrapingError
from API.models.database import Story, Page, Chapter, StoryMetadata, PageMetadata, ChapterMetadata
from API.models.database import ScrapingStatus, StoryStatus, db_manager
from API.services.scraping_service import get_scraper, ScrapingService


class HierarchicalScrapingService(LoggerMixin):
    """Service implementing Story → Page → Chapter hierarchical scraping workflow"""
    
    def __init__(self):
        self.db = db_manager.database
    
    async def scrape_story_hierarchy(self, story_url: str, request: Request, max_pages: int = 10) -> Dict[str, Any]:
        """Complete hierarchical scraping workflow
        
        Args:
            story_url: URL of the story main page
            request: FastAPI request object
            max_pages: Maximum pages to scrape
            
        Returns:
            Dictionary with story, pages, and chapter information
        """
        try:
            # Phase 1: Story Info Scraping
            self.log_info(f"🚀 Phase 1: Scraping story info from {story_url}")
            story_data = await self._scrape_story_info(story_url, request)
            
            # Phase 2: Page Processing
            self.log_info(f"📄 Phase 2: Processing {len(story_data['page_urls'])} pages")
            pages_data = await self._process_pages(story_data, request, max_pages)
            
            # Phase 3: Database Storage
            self.log_info(f"💾 Phase 3: Storing hierarchy in database")
            result = await self._store_hierarchy(story_data, pages_data)
            
            self.log_info(f"✅ Hierarchical scraping completed: {result['story']['title']} "
                         f"({result['total_pages']} pages, {result['total_chapters']} chapters)")
            
            return result
            
        except Exception as e:
            self.log_error(f"❌ Hierarchical scraping failed: {str(e)}")
            raise ScrapingError(f"Hierarchical scraping failed: {str(e)}", url=story_url)
    
    async def _scrape_story_info(self, story_url: str, request: Request) -> Dict[str, Any]:
        """Phase 1: Extract story metadata and generate page URLs"""
        scraper = await get_scraper(request)
        
        # Use existing scraper to get basic story info
        story_info = await scraper.scrape_story_info(story_url)
        
        if not story_info:
            raise ScrapingError("Failed to extract story information", url=story_url)
        
        # Extract total chapters from the page
        total_chapters = await self._extract_total_chapters(story_info, request, story_url)
        
        # Generate page URLs based on pagination
        page_urls = await self._generate_page_urls(story_url, request)
        
        return {
            'url': story_url,
            'title': story_info.get('title', 'Unknown Title'),
            'metadata': {
                'author': story_info.get('metadata', {}).get('author', 'Unknown'),
                'description': story_info.get('metadata', {}).get('description', ''),
                'cover_image_url': story_info.get('metadata', {}).get('cover_image_url') or None,
                'status': story_info.get('metadata', {}).get('status', 'ongoing'),
                'total_chapters': total_chapters,
                'source_website': 'webtruyen',
                'scraped_at': datetime.utcnow().isoformat()
            },
            'page_urls': page_urls,
            'total_pages': len(page_urls)
        }
    
    async def _extract_total_chapters(self, story_info: Dict, request: Request, story_url: str) -> int:
        """Extract total chapters count using the updated selector"""
        try:
            scraper = await get_scraper(request)
            
            # Get a fresh page to extract total chapters
            page = await scraper.scraper_engine.browser_manager.new_page()
            await page.goto(story_url, wait_until='networkidle')
            
            # Try the new total chapters selector
            total_chapters_element = await page.query_selector('span:contains("Chương")')
            if total_chapters_element:
                text = await total_chapters_element.inner_text()
                # Extract number from text like "239 Chương"
                import re
                match = re.search(r'(\d+)\s*Chương', text)
                if match:
                    total_chapters = int(match.group(1))
                    self.log_info(f"📊 Found total chapters: {total_chapters}")
                    return total_chapters
            
            # Fallback to counting chapters from story_info
            chapters = story_info.get('chapters', [])
            if chapters:
                return len(chapters)
            
            self.log_warning("Could not extract total chapters count, defaulting to 0")
            return 0
            
        except Exception as e:
            self.log_error(f"Failed to extract total chapters: {str(e)}")
            return 0
    
    async def _generate_page_urls(self, base_url: str, request: Request) -> List[str]:
        """Generate all page URLs for the story"""
        try:
            scraper = await get_scraper(request)
            
            # Use existing pagination detection
            page = await scraper.scraper_engine.browser_manager.new_page()
            await page.goto(base_url, wait_until='networkidle')
            
            total_pages = await scraper.scraper_engine.strategy.determine_total_pages(page)
            
            page_urls = []
            for page_num in range(1, total_pages + 1):
                if page_num == 1:
                    page_urls.append(base_url)
                else:
                    # Construct page URL (e.g., base_url + "?trang=2")
                    page_url = f"{base_url}?trang={page_num}"
                    page_urls.append(page_url)
            
            self.log_info(f"📄 Generated {len(page_urls)} page URLs")
            return page_urls
            
        except Exception as e:
            self.log_error(f"Failed to generate page URLs: {str(e)}")
            return [base_url]  # Return at least the base URL
    
    async def _process_pages(self, story_data: Dict, request: Request, max_pages: int) -> List[Dict[str, Any]]:
        """Phase 2: Process each page to extract chapter URLs"""
        scraper = await get_scraper(request)
        pages_data = []
        
        page_urls = story_data['page_urls'][:max_pages]  # Limit to max_pages
        
        for page_num, page_url in enumerate(page_urls, 1):
            try:
                self.log_info(f"📄 Processing page {page_num}/{len(page_urls)}: {page_url}")
                
                page = await scraper.scraper_engine.browser_manager.new_page()
                await page.goto(page_url, wait_until='networkidle')
                
                # Extract chapters from this page
                chapters = await scraper.scraper_engine.strategy.collect_chapters_from_page(page, page_num)
                
                chapter_urls = [chapter.get('url', '') for chapter in chapters if chapter.get('url')]
                
                page_data = {
                    'page_number': page_num,
                    'page_url': page_url,
                    'chapter_urls': chapter_urls,
                    'total_chapters_on_page': len(chapter_urls),
                    'chapters_info': chapters,  # Keep full chapter info for later use
                    'scraped_at': datetime.utcnow().isoformat()
                }
                
                pages_data.append(page_data)
                
                self.log_info(f"✅ Page {page_num} processed: {len(chapter_urls)} chapters found")
                
                # Small delay between pages
                await asyncio.sleep(1)
                
            except Exception as e:
                self.log_error(f"❌ Failed to process page {page_num}: {str(e)}")
                # Continue with other pages
                continue
        
        return pages_data
    
    async def _store_hierarchy(self, story_data: Dict, pages_data: List[Dict]) -> Dict[str, Any]:
        """Phase 3: Store the complete hierarchy in database"""
        try:
            # Create or update story
            story_doc = await self._create_story_document(story_data, pages_data)
            story_dict = story_doc.model_dump(by_alias=True, exclude={"id"})
            story_result = await self.db.stories.replace_one(
                {"url": story_data['url']},
                story_dict,
                upsert=True
            )
            
            story_id = story_result.upserted_id or (await self.db.stories.find_one({"url": story_data['url']}))['_id']
            
            # Create page documents
            page_ids = []
            for page_data in pages_data:
                page_doc = await self._create_page_document(story_id, page_data)
                page_dict = page_doc.model_dump(by_alias=True, exclude={"id"})
                page_result = await self.db.pages.replace_one(
                    {"story_id": story_id, "page_number": page_data['page_number']},
                    page_dict,
                    upsert=True
                )
                page_id = page_result.upserted_id or (await self.db.pages.find_one(
                    {"story_id": story_id, "page_number": page_data['page_number']}
                ))['_id']
                page_ids.append(page_id)
                
                # Create chapter documents for this page
                await self._create_chapter_documents(story_id, page_id, page_data)
            
            # Calculate totals
            total_chapters = sum(page['total_chapters_on_page'] for page in pages_data)
            
            # Update story with final counts
            await self.db.stories.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "total_pages": len(pages_data),
                        "total_pages_scraped": len(pages_data),
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            return {
                'story_id': str(story_id),
                'story': story_data,
                'pages': pages_data,
                'total_pages': len(pages_data),
                'total_chapters': total_chapters,
                'page_ids': [str(pid) for pid in page_ids]
            }
            
        except Exception as e:
            self.log_error(f"Failed to store hierarchy: {str(e)}")
            raise ScrapingError(f"Database storage failed: {str(e)}")
    
    async def _create_story_document(self, story_data: Dict, pages_data: List[Dict]) -> Story:
        """Create Story document"""
        cover_url = story_data['metadata'].get('cover_image_url')
        metadata = StoryMetadata(
            author=story_data['metadata']['author'],
            description=story_data['metadata']['description'],
            cover_image_url=cover_url if cover_url else None,
            status=StoryStatus.ONGOING,
            total_chapters=story_data['metadata']['total_chapters'],
            total_pages=len(pages_data),
            source_website='webtruyen',
            scraped_at=datetime.utcnow()
        )
        
        return Story(
            title=story_data['title'],
            url=story_data['url'],
            total_pages=len(pages_data),
            total_pages_scraped=len(pages_data),
            scraping_status=ScrapingStatus.COMPLETED,
            metadata=metadata
        )
    
    async def _create_page_document(self, story_id, page_data: Dict) -> Page:
        """Create Page document"""
        metadata = PageMetadata(
            page_number=page_data['page_number'],
            total_chapters_on_page=page_data['total_chapters_on_page'],
            scraped_at=datetime.utcnow()
        )
        
        return Page(
            story_id=story_id,
            page_number=page_data['page_number'],
            page_url=page_data['page_url'],
            chapter_urls=page_data['chapter_urls'],
            total_chapters_on_page=page_data['total_chapters_on_page'],
            is_scraped=True,
            scraping_status=ScrapingStatus.COMPLETED,
            metadata=metadata
        )
    
    async def _create_chapter_documents(self, story_id, page_id, page_data: Dict):
        """Create Chapter documents for a page, ensuring unique chapter numbers"""
        chapters_info = page_data.get('chapters_info', [])

        # Get the last known chapter number for this story to avoid duplicates
        last_chapter = await self.db.chapters.find_one(
            {"story_id": story_id},
            sort=[("chapter_number", -1)]
        )
        next_chapter_number = (last_chapter["chapter_number"] + 1) if last_chapter else 1

        for chapter_info in chapters_info:
            try:
                chapter_number = chapter_info.get('chapter_number')
                # If chapter number is missing or zero, assign the next available number
                if not chapter_number:
                    chapter_number = next_chapter_number
                    next_chapter_number += 1

                chapter_doc = Chapter(
                    story_id=story_id,
                    page_id=page_id,
                    chapter_number=chapter_number,
                    title=chapter_info.get('title', 'Unknown Chapter'),
                    url=chapter_info.get('url', ''),
                    is_scraped=False,  # Content not scraped yet
                    metadata=ChapterMetadata(
                        source_url=chapter_info.get('url', ''),
                        scraped_at=datetime.utcnow()
                    )
                )

                chapter_dict = chapter_doc.model_dump(by_alias=True, exclude={"id"})
                await self.db.chapters.replace_one(
                    {"story_id": story_id, "chapter_number": chapter_number},
                    chapter_dict,
                    upsert=True
                )

            except Exception as e:
                self.log_error(f"Failed to create chapter document for URL {chapter_info.get('url')}: {str(e)}")
                continue
    
    async def scrape_chapter_content(self, chapter_id: str, request: Request) -> Dict[str, Any]:
        """Phase 3: Scrape individual chapter content"""
        try:
            # Convert string ID to ObjectId
            if isinstance(chapter_id, str):
                chapter_object_id = ObjectId(chapter_id)
            else:
                chapter_object_id = chapter_id
                
            # Get chapter document
            chapter_doc = await self.db.chapters.find_one({"_id": chapter_object_id})
            if not chapter_doc:
                raise ScrapingError(f"Chapter not found: {chapter_id}")
            
            # Use standalone scraper to avoid event loop conflicts with Playwright
            config_path = Path(__file__).parent.parent.parent / "config.yaml"
            standalone_scraper_path = Path(__file__).parent.parent / "tools" / "standalone_scraper.py"
            
            # Run scraping in separate process
            try:
                result = subprocess.run(
                    [sys.executable, str(standalone_scraper_path), chapter_doc['url'], str(config_path)],
                    capture_output=True,
                    text=True,
                    timeout=60  # 60 second timeout
                )
                
                if result.returncode != 0:
                    raise ScrapingError(f"Scraper process failed: {result.stderr}")
                
                content_data = json.loads(result.stdout)
                
                if not content_data.get('success'):
                    raise ScrapingError(f"Scraping failed: {content_data.get('error', 'Unknown error')}")
                    
            except subprocess.TimeoutExpired:
                raise ScrapingError("Scraping timeout after 60 seconds")
            except json.JSONDecodeError as e:
                raise ScrapingError(f"Failed to parse scraper output: {e}")
            except Exception as e:
                raise ScrapingError(f"Subprocess error: {e}")
            
            # Update chapter with content
            await self.db.chapters.update_one(
                {"_id": chapter_object_id},
                {
                    "$set": {
                        "original_content": content_data.get('content', ''),
                        "is_scraped": True,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            return {
                "chapter_id": chapter_id,
                "title": chapter_doc['title'],
                "content": content_data.get('content', ''),
                "scraped_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Failed to scrape chapter content: {str(e)}")
            raise ScrapingError(f"Chapter content scraping failed: {str(e)}")


# Singleton instance
_hierarchical_scraping_service: Optional[HierarchicalScrapingService] = None

def get_hierarchical_scraping_service() -> HierarchicalScrapingService:
    """Get singleton instance of hierarchical scraping service"""
    global _hierarchical_scraping_service
    if _hierarchical_scraping_service is None:
        _hierarchical_scraping_service = HierarchicalScrapingService()
    return _hierarchical_scraping_service