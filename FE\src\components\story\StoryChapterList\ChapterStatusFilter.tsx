'use client';

import React from 'react';

export interface ChapterFilter {
  scraped?: boolean | null; // null = all, true = scraped, false = not scraped
  enhanced?: boolean | null; // null = all, true = enhanced, false = not enhanced
}

interface ChapterStatusFilterProps {
  filter: ChapterFilter;
  onFilterChange: (filter: ChapterFilter) => void;
  className?: string;
}

const ChapterStatusFilter: React.FC<ChapterStatusFilterProps> = ({
  filter,
  onFilterChange,
  className = ''
}) => {
  const handleScrapedFilterChange = (value: string) => {
    const scraped = value === 'all' ? null : value === 'true';
    onFilterChange({ ...filter, scraped });
  };

  const handleEnhancedFilterChange = (value: string) => {
    const enhanced = value === 'all' ? null : value === 'true';
    onFilterChange({ ...filter, enhanced });
  };

  const getScrapedValue = () => {
    if (filter.scraped === null) return 'all';
    return filter.scraped ? 'true' : 'false';
  };

  const getEnhancedValue = () => {
    if (filter.enhanced === null) return 'all';
    return filter.enhanced ? 'true' : 'false';
  };

  return (
    <div className={`bg-zinc-800/50 rounded-lg p-4 border border-zinc-700/50 ${className}`}>
      <div className="flex flex-wrap items-center gap-4">
        <h3 className="text-sm font-medium text-gray-300">Filter:</h3>
        
        {/* Scraped Status Filter */}
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-400">Scrape:</label>
          <select
            value={getScrapedValue()}
            onChange={(e) => handleScrapedFilterChange(e.target.value)}
            className="px-3 py-1 text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All</option>
            <option value="true">Scraped</option>
            <option value="false">Not Scraped</option>
          </select>
        </div>

        {/* Enhanced Status Filter */}
        <div className="flex items-center space-x-2">
          <label className="text-sm text-gray-400">Enhance:</label>
          <select
            value={getEnhancedValue()}
            onChange={(e) => handleEnhancedFilterChange(e.target.value)}
            className="px-3 py-1 text-sm bg-zinc-700 border border-zinc-600 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All</option>
            <option value="true">Enhanced</option>
            <option value="false">Not Enhanced</option>
          </select>
        </div>

        {/* Clear Filters Button */}
        <button
          onClick={() => onFilterChange({ scraped: null, enhanced: null })}
          className="px-3 py-1 text-sm bg-zinc-600 hover:bg-zinc-500 text-white rounded transition-colors"
        >
          Clear Filters
        </button>
      </div>

      {/* Active Filters Display */}
      <div className="mt-3 flex flex-wrap gap-2">
        {filter.scraped !== null && (
          <span className="inline-flex items-center px-2 py-1 text-xs bg-green-900/30 text-green-400 rounded">
            Scraped: {filter.scraped ? 'Yes' : 'No'}
            <button
              onClick={() => onFilterChange({ ...filter, scraped: null })}
              className="ml-1 text-green-300 hover:text-green-200"
            >
              ×
            </button>
          </span>
        )}
        {filter.enhanced !== null && (
          <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-900/30 text-blue-400 rounded">
            Enhanced: {filter.enhanced ? 'Yes' : 'No'}
            <button
              onClick={() => onFilterChange({ ...filter, enhanced: null })}
              className="ml-1 text-blue-300 hover:text-blue-200"
            >
              ×
            </button>
          </span>
        )}
      </div>
    </div>
  );
};

export default ChapterStatusFilter;