'use client';

import { useEffect, useState } from 'react';
import { fetchStories } from '@/services/storyService';
import { Story } from '@/types/story';
import StoryCard from './StoryCard';
import Pagination from '@/components/common/Pagination';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

const StoryList = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  const loadStories = async (pageNum: number = 1) => {
    try {
      setLoading(true);
      const response = await fetchStories(pageNum, 12);

      setStories(response.data);
      setCurrentPage(pageNum);
      setTotalPages(response.pagination.total_pages);
      setTotalItems(response.pagination.total_items);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch stories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStories(1);
  }, []);

  const handlePageChange = (pageNum: number) => {
    if (pageNum !== currentPage && !loading) {
      loadStories(pageNum);
      // Scroll to top when page changes
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  if (loading && stories.length === 0) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mt-8">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="overflow-hidden">
            <Skeleton className="aspect-[3/4] w-full" />
            <CardContent className="p-4">
              <Skeleton className="h-6 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-3" />
              <div className="flex justify-between">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-3 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center mt-8">
        <Card className="border-destructive/50 bg-destructive/10">
          <CardContent className="p-6">
            <h3 className="text-destructive font-semibold mb-2">Lỗi tải dữ liệu</h3>
            <p className="text-destructive/80 mb-4">{error}</p>
            <Button
              onClick={() => loadStories(1)}
              variant="destructive"
            >
              Thử lại
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (stories.length === 0) {
    return (
      <div className="text-center mt-8">
        <Card>
          <CardContent className="p-8">
            <svg className="w-16 h-16 text-muted-foreground mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
            </svg>
            <h3 className="text-foreground font-semibold mb-2">Chưa có truyện nào</h3>
            <p className="text-muted-foreground mb-4">Hãy bắt đầu bằng cách cào một truyện mới!</p>
            <Button asChild>
              <a href="/scrape">
                Cào truyện mới
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mt-8">
      {/* Stories count and pagination info */}
      {totalItems > 0 && (
        <div className="flex justify-between items-center mb-6">
          <p className="text-gray-400 text-sm">
            Hiển thị {((currentPage - 1) * 12) + 1}-{Math.min(currentPage * 12, totalItems)} trong tổng số {totalItems} truyện
          </p>
          <p className="text-gray-400 text-sm">
            Trang {currentPage} / {totalPages}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {stories.map((story) => (
          <StoryCard key={story.id} story={story} />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="justify-center"
          />
        </div>
      )}
    </div>
  );
};

export default StoryList;