'use client';

import React from 'react';
import { Chapter } from '@/types/story';
import { ChapterListItem } from './ChapterListItem';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ChapterListProps {
  storyId: string;
  chapters: Chapter[];
  loading: boolean;
  selectedChapters: Set<string>;
  selectionMode: 'scraping' | 'enhancement' | null;
  onSelectChapter: (chapterId: string) => void;
  onSelectAll: (mode: 'scraping' | 'enhancement') => void;
  onDeselectAll: () => void;
}

export const ChapterList: React.FC<ChapterListProps> = ({ 
  storyId, 
  chapters, 
  loading, 
  selectedChapters, 
  selectionMode, 
  onSelectChapter, 
  onSelectAll, 
  onDeselectAll 
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (chapters.length === 0) {
    return <p className="text-center p-8 text-gray-500">No chapters found.</p>;
  }

  return (
    <div>
      {selectionMode && (
        <div className="flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-800 rounded-t-lg">
          <p className="text-sm font-medium">
            {selectionMode === 'scraping' ? 'Select chapters to scrape' : 'Select chapters to enhance'}
          </p>
          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={() => onSelectAll(selectionMode)}>Select All Visible</Button>
            <Button size="sm" variant="outline" onClick={onDeselectAll}>Clear Selection</Button>
          </div>
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        {chapters.map(chapter => (
          <ChapterListItem 
            key={chapter.id}
            storyId={storyId}
            chapter={chapter}
            isSelected={selectedChapters.has(chapter.id)}
            selectionMode={selectionMode}
            onSelectChapter={onSelectChapter}
          />
        ))}
      </div>
    </div>
  );
};
