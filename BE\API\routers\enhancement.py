"""
AI Content Enhancement API Endpoints

This module provides endpoints for enhancing Vietnamese web novel content
using AI, including batch processing and progress tracking.
"""

import asyncio
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from API.models.schemas import (
    ChapterEnhancementRequest, BatchEnhancementRequest,
    BatchEnhancementResponse, EnhancementResult, APIResponse
)
from API.models.database import Chapter, EnhancementJob, EnhancementStatus, ScrapingStatus
from API.utils.database import chapter_service, enhancement_job_service, story_service
from API.services.enhancement_service import EnhancementService
from API.middleware.error_handling import AIEnhancementError, DatabaseError
from API.utils.logging_config import LoggerMixin
from API.core.config import get_settings

router = APIRouter()
settings = get_settings()


class EnhancementController(LoggerMixin):
    """Controller for AI enhancement operations"""
    
    def __init__(self):
        self.enhancement_service = EnhancementService()
    
    async def enhance_single_chapter(
        self,
        request: ChapterEnhancementRequest
    ) -> APIResponse:
        """Enhance a single chapter's content"""
        try:
            self.log_info(f"Starting single chapter enhancement for: {request.chapter_id}")
            
            # Get chapter from database
            chapter = await chapter_service.find_by_id(request.chapter_id)
            if not chapter:
                raise HTTPException(status_code=404, detail="Chapter not found")
            
            # Check if chapter has content to enhance
            if not chapter.get("original_content"):
                raise AIEnhancementError("Chapter has no content to enhance")
            
            # Check if already enhanced and not forcing re-enhancement
            if chapter.get("is_enhanced") and not request.force_re_enhance:
                return APIResponse(
                    success=True,
                    message="Chapter is already enhanced. Use force_re_enhance=true to re-enhance."
                )
            
            # Enhance the content
            enhancement_result = await self.enhancement_service.enhance_chapter(
                chapter_id=request.chapter_id,
                chapter_title=chapter["title"],
                content=chapter["original_content"]
            )
            
            if enhancement_result["success"]:
                # Update chapter in database
                await self._update_enhanced_chapter(request.chapter_id, enhancement_result)
                
                return APIResponse(
                    success=True,
                    message="Chapter enhanced successfully"
                )
            else:
                raise AIEnhancementError(f"Enhancement failed: {enhancement_result.get('error')}")
                
        except Exception as e:
            self.log_error(f"Error enhancing single chapter: {e}")
            if isinstance(e, HTTPException):
                raise
            raise AIEnhancementError(f"Failed to enhance chapter: {e}")
    
    async def batch_enhance_chapters(
        self,
        request: BatchEnhancementRequest,
        background_tasks: BackgroundTasks
    ) -> BatchEnhancementResponse:
        """Enhance multiple chapters in batch"""
        try:
            self.log_info("Starting batch chapter enhancement")
            
            # Get chapters to enhance
            chapters_to_enhance = await self._get_chapters_for_enhancement(request)
            
            if not chapters_to_enhance:
                return BatchEnhancementResponse(
                    success=True,
                    message="No chapters found for enhancement",
                    job_id="",
                    total_chapters=0
                )
            
            # Create enhancement job
            job_data = {
                "story_id": request.story_id,
                "chapter_ids": [str(chapter["_id"]) for chapter in chapters_to_enhance],
                "total_chapters": len(chapters_to_enhance),
                "batch_size": request.batch_size,
                "model_name": settings.ai_model_name
            }
            
            job_id = await enhancement_job_service.create_job(job_data)
            
            # Start background enhancement
            background_tasks.add_task(
                self._background_batch_enhancement,
                job_id,
                chapters_to_enhance,
                request.force_re_enhance,
                request.batch_size
            )
            
            return BatchEnhancementResponse(
                success=True,
                message="Batch enhancement started",
                job_id=str(job_id),
                total_chapters=len(chapters_to_enhance)
            )
            
        except Exception as e:
            self.log_error(f"Error starting batch enhancement: {e}")
            raise AIEnhancementError(f"Failed to start batch enhancement: {e}")
    
    async def _get_chapters_for_enhancement(
        self,
        request: BatchEnhancementRequest
    ) -> List[dict]:
        """Get chapters that need enhancement based on request"""
        try:
            chapters = []
            
            if request.chapter_ids:
                # Enhance specific chapters
                for chapter_id in request.chapter_ids:
                    chapter = await chapter_service.find_by_id(chapter_id)
                    if chapter and chapter.get("original_content"):
                        # Skip if already enhanced and not forcing
                        if not chapter.get("is_enhanced") or request.force_re_enhance:
                            chapters.append(chapter)
            
            elif request.story_id:
                # Enhance all chapters of a story
                story_chapters = await chapter_service.get_enhancement_candidates(
                    story_id=request.story_id
                )
                
                for chapter in story_chapters:
                    # Skip if already enhanced and not forcing
                    if not chapter.get("is_enhanced") or request.force_re_enhance:
                        chapters.append(chapter)
            
            self.log_info(f"Found {len(chapters)} chapters for enhancement")
            return chapters
            
        except Exception as e:
            self.log_error(f"Error getting chapters for enhancement: {e}")
            raise DatabaseError(f"Failed to get chapters for enhancement: {e}")
    
    async def _background_batch_enhancement(
        self,
        job_id: str,
        chapters: List[dict],
        force_re_enhance: bool,
        batch_size: int
    ):
        """Background task for batch chapter enhancement"""
        try:
            await enhancement_job_service.update_job_progress(
                job_id,
                completed_items=0,
                status="in_progress"
            )
            
            completed = 0
            failed = 0
            errors = []
            results = []
            
            # Process chapters in batches to avoid overwhelming the AI service
            for i in range(0, len(chapters), batch_size):
                batch = chapters[i:i + batch_size]
                
                # Process batch sequentially to respect rate limits
                for chapter in batch:
                    try:
                        self.log_info(f"Enhancing chapter: {chapter['title']}")
                        
                        # Enhance chapter
                        enhancement_result = await self.enhancement_service.enhance_chapter(
                            chapter_id=str(chapter["_id"]),
                            chapter_title=chapter["title"],
                            content=chapter["original_content"]
                        )
                        
                        if enhancement_result["success"]:
                            # Update chapter in database
                            await self._update_enhanced_chapter(
                                str(chapter["_id"]),
                                enhancement_result
                            )
                            
                            # Add to results
                            result = EnhancementResult(
                                chapter_id=str(chapter["_id"]),
                                original_length=len(chapter["original_content"]),
                                enhanced_length=len(enhancement_result["enhanced_content"]),
                                improvement_notes=enhancement_result.get("improvement_notes", []),
                                enhancement_duration=enhancement_result.get("duration"),
                                quality_score=enhancement_result.get("quality_score")
                            )
                            results.append(result.dict())
                            
                            completed += 1
                            self.log_info(f"Successfully enhanced chapter: {chapter['title']}")
                            
                        else:
                            failed += 1
                            error_msg = f"Failed to enhance {chapter['title']}: {enhancement_result.get('error')}"
                            errors.append(error_msg)
                            self.log_error(error_msg)
                        
                        # Update job progress
                        await enhancement_job_service.update_job_progress(
                            job_id,
                            completed_items=completed,
                            failed_items=failed,
                            errors=errors[-10:],  # Keep only last 10 errors
                            results={"enhanced_chapters": results}
                        )
                        
                        # Add delay between enhancements to respect rate limits
                        await asyncio.sleep(settings.ai_rate_limit_delay)
                        
                    except Exception as e:
                        failed += 1
                        error_msg = f"Exception enhancing {chapter['title']}: {e}"
                        errors.append(error_msg)
                        self.log_error(error_msg)
                
                # Add longer delay between batches
                if i + batch_size < len(chapters):
                    await asyncio.sleep(settings.ai_rate_limit_delay * 2)
            
            # Update story enhancement progress
            if chapters and chapters[0].get("story_id"):
                await story_service.update_enhancement_progress(
                    chapters[0]["story_id"],
                    completed
                )
            
            # Mark job as completed
            final_status = "completed" if failed == 0 else "completed_with_errors"
            await enhancement_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status=final_status,
                errors=errors,
                results={"enhanced_chapters": results}
            )
            
            self.log_info(f"Batch enhancement job {job_id} completed: {completed} success, {failed} failed")
            
        except Exception as e:
            self.log_error(f"Error in background batch enhancement: {e}")
            await enhancement_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status="failed",
                errors=[str(e)]
            )
    
    async def _update_enhanced_chapter(
        self,
        chapter_id: str,
        enhancement_result: dict
    ):
        """Update chapter with enhanced content"""
        try:
            update_data = {
                "$set": {
                    "enhanced_content": enhancement_result["enhanced_content"],
                    "is_enhanced": True,
                    "enhancement_status": EnhancementStatus.ENHANCED,
                    "enhancement_metadata": {
                        "enhanced_at": datetime.utcnow(),
                        "enhancement_duration": enhancement_result.get("duration"),
                        "model_used": enhancement_result.get("model_name", settings.ai_model_name),
                        "enhancement_version": "1.0",
                        "quality_score": enhancement_result.get("quality_score"),
                        "improvement_notes": enhancement_result.get("improvement_notes", []),
                        "original_length": enhancement_result.get("original_length"),
                        "enhanced_length": enhancement_result.get("enhanced_length"),
                        "similarity_score": enhancement_result.get("similarity_score")
                    }
                }
            }
            
            await chapter_service.update_by_id(chapter_id, update_data)
            
        except Exception as e:
            self.log_error(f"Error updating enhanced chapter: {e}")
            raise DatabaseError(f"Failed to update enhanced chapter: {e}")


# ============================================================================
# Router Endpoints
# ============================================================================

controller = EnhancementController()


@router.post("/single", response_model=APIResponse)
async def enhance_single_chapter(request: ChapterEnhancementRequest):
    """
    Enhance a single chapter's content using AI
    
    This endpoint takes a chapter ID and enhances its content using
    Google Gemini AI to improve Vietnamese text quality and readability.
    """
    return await controller.enhance_single_chapter(request)


@router.post("/batch", response_model=BatchEnhancementResponse)
async def batch_enhance_chapters(
    request: BatchEnhancementRequest,
    background_tasks: BackgroundTasks
):
    """
    Enhance multiple chapters in batch
    
    This endpoint accepts either a list of chapter IDs or a story ID
    and enhances all specified chapters using AI with progress tracking.
    """
    return await controller.batch_enhance_chapters(request, background_tasks)


@router.get("/test-enhancement")
async def test_enhancement():
    """Test endpoint to verify AI enhancement functionality"""
    try:
        enhancement_service = EnhancementService()
        status = await enhancement_service.test_connection()
        
        return APIResponse(
            success=True,
            message=f"Enhancement service status: {status}"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Enhancement service unavailable: {e}"
        )
