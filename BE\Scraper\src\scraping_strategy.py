# d:\Personal Projects\Vibe\Webtruyen\BE\Scraper\src\scraping_strategy.py

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from playwright.async_api import Page

class ScrapingStrategy(ABC):
    """Abstract base class for a scraping strategy."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    @abstractmethod
    async def extract_story_info(self, page: Page) -> Dict[str, Any]:
        """Extracts comprehensive story information from the page."""
        pass

    @abstractmethod
    async def determine_total_pages(self, page: Page) -> int:
        """Determines the total number of chapter pages."""
        pass

    @abstractmethod
    async def collect_chapters_from_page(self, page: Page, page_num: int) -> List[Dict[str, str]]:
        """Collects chapter information from a single page."""
        pass

import re
import time
from urllib.parse import urljoin
from loguru import logger

class WebtruyenStrategy(ScrapingStrategy):
    """Scraping strategy for webtruyen.com."""

    async def extract_story_info(self, page: Page, url: str) -> Dict[str, Any]:
        logger.info(f"🔍 WebtruyenStrategy config keys: {list(self.config.keys())}")
        selectors = self.config.get('selectors', {})
        logger.info(f"🔍 Selectors loaded: {len(selectors)} items - {list(selectors.keys())}")
        story_info = {
            'url': url,
            'title': '',
            'chapters': [],  # Initialize empty chapters list
            'metadata': {
                'source_website': 'webtruyen',
                'scraped_at': time.time(),
                'status': 'ongoing'
            }
        }
        
        await self._extract_title(page, story_info, selectors)
        await self._extract_description(page, story_info, selectors)
        await self._extract_cover_image(page, story_info, selectors, url)
        await self._extract_additional_metadata(page, story_info, selectors)

        logger.info(f"📖 Story info extracted: {story_info['title']}")
        return story_info

    async def _extract_title(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            logger.info(f"🔍 Extracting title with selectors: {selectors}")
            if 'story_title' in selectors:
                selector = selectors['story_title']
                logger.info(f"📝 Trying story_title selector: {selector}")
                title_element = await page.query_selector(selector)
                if title_element:
                    title_text = await title_element.inner_text()
                    logger.info(f"✅ Found title: '{title_text}'")
                    story_info['title'] = title_text.strip()
                    return
                else:
                    logger.warning(f"❌ No element found for story_title selector: {selector}")
            if 'title' in selectors:
                selector = selectors['title']
                logger.info(f"📝 Trying fallback title selector: {selector}")
                title_element = await page.query_selector(selector)
                if title_element:
                    title_text = await title_element.inner_text()
                    logger.info(f"✅ Found title with fallback: '{title_text}'")
                    story_info['title'] = title_text.strip()
                    return
                else:
                    logger.warning(f"❌ No element found for title selector: {selector}")
            
            # Fallback to page title if selectors don't work
            logger.info(f"📄 Trying page title as fallback")
            page_title = await page.title()
            if page_title and page_title != '404 - Hội Nhiều Chữ':
                # Extract story title from page title (remove site suffix)
                title_parts = page_title.split(' – ')
                if len(title_parts) > 1:
                    story_info['title'] = title_parts[0].strip()
                else:
                    story_info['title'] = page_title.strip()
                logger.info(f"✅ Extracted title from page title: '{story_info['title']}'")
                return
            
            logger.warning(f"❌ Could not extract title from any source")
            story_info['title'] = "Unknown Title"
        except Exception as e:
            logger.error(f"❌ Failed to extract title: {e}")
            story_info['title'] = "Unknown Title"

    async def _extract_description(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            description = ""
            if 'story_description' in selectors:
                desc_element = await page.query_selector(selectors['story_description'])
                if desc_element:
                    description = (await desc_element.inner_text()).strip()
                    logger.info(f"✅ Found description: '{description[:100]}...'")
                else:
                    logger.warning(f"❌ No element found for story_description selector: {selectors['story_description']}")

            # Fallback to meta description if no story description found
            if not description and 'meta_description' in selectors:
                meta_desc = await page.query_selector(selectors['meta_description'])
                if meta_desc:
                    description = (await meta_desc.get_attribute('content') or "").strip()
                    logger.info(f"✅ Found meta description: '{description[:100]}...'")

            # Store description in both places for compatibility
            story_info['description'] = description
            if 'metadata' not in story_info:
                story_info['metadata'] = {}
            story_info['metadata']['description'] = description
        except Exception as e:
            logger.warning(f"Failed to extract description: {e}")
            story_info['metadata']['description'] = ""

    async def _extract_cover_image(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str], url: str) -> None:
        try:
            cover_image_url = None
            if 'story_image' in selectors:
                img_element = await page.query_selector(selectors['story_image'])
                if img_element:
                    img_src = await img_element.get_attribute('src')
                    if img_src:
                        cover_image_url = self._make_absolute_url(img_src, url)
                        logger.info(f"✅ Found cover image: '{cover_image_url}'")
                    else:
                        logger.warning(f"❌ Image element found but no src attribute")
                else:
                    logger.warning(f"❌ No element found for story_image selector: {selectors['story_image']}")

            # Fallback: try to find any image with https://i0.wp prefix
            if not cover_image_url:
                all_images = await page.query_selector_all('img')
                for img in all_images:
                    src = await img.get_attribute('src')
                    if src and src.startswith('https://i0.wp'):
                        cover_image_url = src
                        logger.info(f"✅ Found cover image via fallback: '{cover_image_url}'")
                        break

            story_info['metadata']['cover_image_url'] = cover_image_url
        except Exception as e:
            logger.warning(f"Failed to extract cover image: {e}")
            story_info['metadata']['cover_image_url'] = None

    async def _extract_additional_metadata(self, page: Page, story_info: Dict[str, Any], selectors: Dict[str, str]) -> None:
        try:
            # Extract author using multiple approaches
            author = await self._extract_author_robust(page, selectors)
            story_info['author'] = author
            story_info['metadata']['author'] = author

            # Extract total chapters count
            total_chapters = await self._extract_total_chapters_count(page, selectors)
            story_info['metadata']['total_chapters'] = total_chapters

            page_meta = await self._extract_page_metadata(page, selectors)
            story_info['metadata'].update(page_meta)
        except Exception as e:
            logger.warning(f"Failed to extract additional metadata: {e}")
            story_info['metadata']['author'] = "Unknown"
    
    async def _extract_author_robust(self, page: Page, selectors: Dict[str, str]) -> str:
        """Extract author using multiple fallback approaches"""
        author = "Unknown"
        
        # Method 1: Try configured selector first
        if 'story_author' in selectors:
            try:
                author_element = await page.query_selector(selectors['story_author'])
                if author_element:
                    author = (await author_element.inner_text()).strip()
                    logger.info(f"✅ Found author with selector: '{author}'")
                    return author
            except Exception as e:
                logger.warning(f"❌ Error with configured author selector: {e}")
        
        # Method 2: Use JavaScript to find text containing "Tác giả:"
        try:
            author_js = await page.evaluate("""
                () => {
                    // Look for elements containing "Tác giả:" text
                    const elements = Array.from(document.querySelectorAll('*'));
                    for (const el of elements) {
                        const text = el.textContent || '';
                        if (text.includes('Tác giả:')) {
                            // Try to find a link within this element
                            const link = el.querySelector('a[href*="tac-gia"]');
                            if (link) {
                                return link.textContent.trim();
                            }
                            // Or extract text after "Tác giả:"
                            const match = text.match(/Tác giả:\s*([^\n\r]+)/);
                            if (match) {
                                return match[1].trim();
                            }
                        }
                    }
                    return null;
                }
            """)
            if author_js:
                author = author_js.strip()
                logger.info(f"✅ Found author with JavaScript: '{author}'")
                return author
        except Exception as e:
            logger.warning(f"❌ Error with JavaScript author extraction: {e}")
        
        # Method 3: Fallback to common author selectors
        fallback_selectors = [
            'a[href*="tac-gia"]',
            '.author a',
            '.story-author',
            '.author-name',
            '[class*="author"] a'
        ]
        
        for selector in fallback_selectors:
            try:
                author_element = await page.query_selector(selector)
                if author_element:
                    author = (await author_element.inner_text()).strip()
                    if author and author != "Unknown":
                        logger.info(f"✅ Found author with fallback selector '{selector}': '{author}'")
                        return author
            except Exception:
                continue
        
        logger.warning("❌ Could not extract author with any method")
        return "Unknown"

    async def _extract_page_metadata(self, page: Page, selectors: Dict[str, str]) -> Dict[str, Any]:
        metadata = {}
        try:
            metadata['page_title'] = await page.title()
            if 'meta_description' in selectors:
                desc_element = await page.query_selector(selectors['meta_description'])
                if desc_element:
                    metadata['meta_description'] = await desc_element.get_attribute('content')
            if 'meta_keywords' in selectors:
                keywords_element = await page.query_selector(selectors['meta_keywords'])
                if keywords_element:
                    metadata['keywords'] = await keywords_element.get_attribute('content')
        except Exception as e:
            logger.warning(f"Page metadata extraction failed: {e}")
        return metadata

    async def determine_total_pages(self, page: Page) -> int:
        selectors = self.config.get('selectors', {})
        try:
            logger.info(f"🔍 Determining total pages using selector: {selectors.get('story_pagination', 'None')}")
            if 'story_pagination' in selectors:
                pagination_container = await page.query_selector(selectors['story_pagination'])
                if pagination_container:
                    total_pages = await self._extract_max_page_from_pagination(pagination_container)
                    logger.info(f"✅ Found {total_pages} total pages")
                    return total_pages
                else:
                    logger.warning(f"❌ No pagination container found with selector: {selectors['story_pagination']}")

            logger.info(f"📄 Defaulting to 1 page")
            return 1
        except Exception as e:
            logger.error(f"Error determining total pages: {e}")
            return 1

    async def _extract_max_page_from_pagination(self, pagination_container) -> int:
        max_page = 1
        try:
            logger.info(f"🔍 Extracting max page from pagination container")
            last_links = await pagination_container.query_selector_all('a')
            logger.info(f"📄 Found {len(last_links)} pagination links")

            for i, link in enumerate(last_links):
                try:
                    text = (await link.inner_text()).strip()
                    href = await link.get_attribute('href') or ""
                    logger.info(f"🔗 Link {i+1}: text='{text}', href='{href[:50]}...'")

                    # Check for "last" indicators in Vietnamese
                    if any(indicator in text.lower() for indicator in ['cuối', 'last', 'end', '»', '>>']):
                        if href and 'trang=' in href:
                            match = re.search(r'trang=(\d+)', href)
                            if match:
                                page_num = int(match.group(1))
                                max_page = max(max_page, page_num)
                                logger.info(f"✅ Found last page indicator: {page_num}")

                    # Check for numeric page links
                    elif text.isdigit():
                        page_num = int(text)
                        max_page = max(max_page, page_num)
                        logger.info(f"✅ Found numeric page: {page_num}")

                    # Also check href for page numbers even if text isn't numeric
                    elif href and 'trang=' in href:
                        match = re.search(r'trang=(\d+)', href)
                        if match:
                            page_num = int(match.group(1))
                            max_page = max(max_page, page_num)
                            logger.info(f"✅ Found page in href: {page_num}")

                except Exception as link_error:
                    logger.warning(f"Error processing pagination link {i+1}: {link_error}")

            logger.info(f"🎯 Final max page determined: {max_page}")
        except Exception as e:
            logger.error(f"Error extracting max page from pagination: {e}")
        return max_page

    async def collect_chapters_from_page(self, page: Page, page_num: int) -> List[Dict[str, str]]:
        selectors = self.config.get('selectors', {})
        chapters = []
        try:
            logger.info(f"📚 Collecting chapters from page {page_num}")
            chapter_list_selector = selectors.get('story_chapters') or selectors.get('chapter_list')

            if not chapter_list_selector:
                logger.error(f"❌ No chapter list selector configured")
                return chapters

            if 'chapter_link' not in selectors:
                logger.error(f"❌ No chapter link selector configured")
                return chapters

            logger.info(f"🔍 Using chapter list selector: {chapter_list_selector}")
            chapter_list = await page.query_selector(chapter_list_selector)

            if not chapter_list:
                logger.warning(f"❌ No chapter list found with selector: {chapter_list_selector}")
                return chapters

            logger.info(f"🔍 Using chapter link selector: {selectors['chapter_link']}")
            chapter_links = await chapter_list.query_selector_all(selectors['chapter_link'])
            logger.info(f"📄 Found {len(chapter_links)} chapter links on page {page_num}")

            for i, link in enumerate(chapter_links):
                try:
                    chapter_info = await self._extract_chapter_info(link, page)
                    if chapter_info:
                        chapter_info['page_number'] = page_num  # Add page number to chapter info
                        chapters.append(chapter_info)
                        logger.info(f"✅ Chapter {i+1}: {chapter_info.get('title', 'No title')[:50]}...")
                    else:
                        logger.warning(f"❌ Failed to extract info for chapter link {i+1}")
                except Exception as link_error:
                    logger.error(f"❌ Error processing chapter link {i+1}: {link_error}")

            logger.info(f"✅ Successfully collected {len(chapters)} chapters from page {page_num}")
        except Exception as e:
            logger.error(f"Error extracting chapters from page {page_num}: {e}")
        return chapters

    async def _extract_chapter_info(self, link_element, page: Page) -> Optional[Dict[str, Any]]:
        try:
            href = await link_element.get_attribute('href')
            title = await link_element.inner_text()

            if not href:
                logger.warning(f"❌ Chapter link has no href attribute")
                return None

            if not title or not title.strip():
                logger.warning(f"❌ Chapter link has no title text")
                return None

            absolute_url = self._make_absolute_url(href, page.url)
            clean_title = title.strip()

            # Try to extract chapter number from title
            chapter_number = self._extract_chapter_number(clean_title)

            return {
                'url': absolute_url,
                'title': clean_title,
                'chapter_number': chapter_number
            }
        except Exception as e:
            logger.warning(f"Error extracting chapter info: {e}")
        return None

    def _extract_chapter_number(self, title: str) -> int:
        """Extract chapter number from title"""
        try:
            # Look for patterns like "Chương 1", "Chapter 1", "1.", etc.
            patterns = [
                r'chương\s*(\d+)',
                r'chapter\s*(\d+)',
                r'^(\d+)\.',
                r'(\d+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, title.lower())
                if match:
                    return int(match.group(1))
        except Exception as e:
            logger.warning(f"Error extracting chapter number from '{title}': {e}")

        return 0  # Default chapter number

    async def _extract_total_chapters_count(self, page: Page, selectors: Dict[str, str]) -> int:
        """Extract total chapters count from the page using multiple approaches."""
        try:
            # Method 1: Try configured selector first
            if 'story_total_chapters' in selectors:
                total_chapters_element = await page.query_selector(selectors['story_total_chapters'])
                if total_chapters_element:
                    text = (await total_chapters_element.inner_text()).strip()
                    count = self._extract_number_from_text(text)
                    if count > 0:
                        logger.info(f"✅ Found total chapters with selector: {count} from text: '{text}'")
                        return count
            
            # Method 2: Use JavaScript to find text containing "Chương" or chapter indicators
            try:
                chapter_count_js = await page.evaluate("""
                    () => {
                        // Look for elements containing chapter count indicators
                        const elements = Array.from(document.querySelectorAll('*'));
                        const patterns = [
                            /([0-9]+)\s*(?:chương|ch\b)/i,
                            /(?:tổng|total)\s*:?\s*([0-9]+)/i,
                            /([0-9]+)\s*(?:chapters?)/i
                        ];
                        
                        for (const el of elements) {
                            const text = el.textContent || '';
                            for (const pattern of patterns) {
                                const match = text.match(pattern);
                                if (match) {
                                    const num = parseInt(match[1]);
                                    if (num > 0 && num < 10000) { // Reasonable range
                                        return num;
                                    }
                                }
                            }
                        }
                        return 0;
                    }
                """)
                if chapter_count_js > 0:
                    logger.info(f"✅ Found total chapters with JavaScript: {chapter_count_js}")
                    return chapter_count_js
            except Exception as e:
                logger.warning(f"❌ Error with JavaScript chapter count extraction: {e}")
            
            # Method 3: Count chapter links on current page and estimate
            try:
                chapter_links = await page.query_selector_all('a[href*="chuong-"], a[href*="chapter-"], a[class*="uk-link-toggle"]')
                estimated_count = len(chapter_links)
                if estimated_count > 0:
                    logger.info(f"📊 Estimated total chapters from links: {estimated_count}")
                    return estimated_count
            except Exception as e:
                logger.warning(f"Error estimating chapters from links: {e}")
            
        except Exception as e:
            logger.warning(f"Error extracting total chapters count: {e}")
        
        logger.warning("❌ Could not determine total chapters count")
        return 0
    
    def _extract_number_from_text(self, text: str) -> int:
        """Extract number from text using various patterns"""
        if not text:
            return 0
        
        # Try different patterns
        patterns = [
            r'(\d+)\s*(?:ch|chương)\b',  # "239 ch", "239 Chương"
            r'(?:tổng|total)\s*:?\s*(\d+)',  # "Total: 123", "Tổng 123"
            r'(\d+)\s*(?:chapters?)\b',  # "123 chapters"
            r'\b(\d+)\b'  # Any number
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                num = int(match.group(1))
                if 0 < num < 10000:  # Reasonable range for chapter count
                    return num
        
        return 0

    def _make_absolute_url(self, href: str, current_url: str) -> str:
        if href.startswith('http'):
            return href
        elif href.startswith('/'):
            base_url = self.config.get('base_url', '')
            return base_url + href
        else:
            return urljoin(current_url, href)