#!/usr/bin/env python3
"""
Database Migration Script for Hierarchical Structure

This script migrates existing data to the new Story → Page → Chapter hierarchy
and cleans up any conflicting data to ensure a fresh start.

Usage:
    python scripts/migrate_to_hierarchical.py [--dry-run] [--backup]
"""

import asyncio
import argparse
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from API.models.database import db_manager
from API.utils.logging_config import setup_logging
import logging

logger = logging.getLogger(__name__)


class HierarchicalMigration:
    """Handles migration to hierarchical database structure"""
    
    def __init__(self, dry_run: bool = False, backup: bool = True):
        self.dry_run = dry_run
        self.backup = backup
        self.stats = {
            'stories_processed': 0,
            'chapters_processed': 0,
            'pages_created': 0,
            'stories_cleaned': 0,
            'chapters_cleaned': 0,
            'errors': []
        }
    
    async def run_migration(self):
        """Run the complete migration process"""
        try:
            logger.info("🚀 Starting hierarchical database migration")
            
            if self.backup:
                await self._create_backup()
            
            await self._clean_existing_data()
            await self._migrate_stories()
            
            self._print_summary()
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            raise
    
    async def _create_backup(self):
        """Create backup of existing data"""
        if self.dry_run:
            logger.info("🔍 [DRY RUN] Would create backup of existing data")
            return
        
        logger.info("💾 Creating backup of existing data...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Backup stories
        stories = await db_manager.database.stories.find({}).to_list(None)
        if stories:
            backup_collection = f"stories_backup_{timestamp}"
            await db_manager.database[backup_collection].insert_many(stories)
            logger.info(f"✅ Backed up {len(stories)} stories to {backup_collection}")
        
        # Backup chapters
        chapters = await db_manager.database.chapters.find({}).to_list(None)
        if chapters:
            backup_collection = f"chapters_backup_{timestamp}"
            await db_manager.database[backup_collection].insert_many(chapters)
            logger.info(f"✅ Backed up {len(chapters)} chapters to {backup_collection}")
    
    async def _clean_existing_data(self):
        """Clean existing data to avoid conflicts"""
        logger.info("🧹 Cleaning existing data for fresh hierarchical structure...")
        
        if self.dry_run:
            # Count what would be deleted
            story_count = await db_manager.database.stories.count_documents({})
            chapter_count = await db_manager.database.chapters.count_documents({})
            page_count = await db_manager.database.pages.count_documents({})
            
            logger.info(f"🔍 [DRY RUN] Would delete:")
            logger.info(f"  - {story_count} stories")
            logger.info(f"  - {chapter_count} chapters") 
            logger.info(f"  - {page_count} pages")
            return
        
        # Delete existing data
        story_result = await db_manager.database.stories.delete_many({})
        chapter_result = await db_manager.database.chapters.delete_many({})
        page_result = await db_manager.database.pages.delete_many({})
        
        self.stats['stories_cleaned'] = story_result.deleted_count
        self.stats['chapters_cleaned'] = chapter_result.deleted_count
        
        logger.info(f"✅ Cleaned {story_result.deleted_count} stories")
        logger.info(f"✅ Cleaned {chapter_result.deleted_count} chapters")
        logger.info(f"✅ Cleaned {page_result.deleted_count} pages")
    
    async def _migrate_stories(self):
        """Migrate stories to new hierarchical structure"""
        logger.info("📚 Migrating stories to hierarchical structure...")
        
        if self.dry_run:
            logger.info("🔍 [DRY RUN] Would migrate stories to new structure")
            return
        
        # Since we cleaned the data, this is mainly for future reference
        # The actual migration will happen when users re-scrape stories
        logger.info("✅ Stories ready for hierarchical scraping")
    

    
    def _print_summary(self):
        """Print migration summary"""
        logger.info("\n" + "="*60)
        logger.info("📊 MIGRATION SUMMARY")
        logger.info("="*60)
        logger.info(f"Stories cleaned: {self.stats['stories_cleaned']}")
        logger.info(f"Chapters cleaned: {self.stats['chapters_cleaned']}")
        logger.info(f"Errors: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            logger.warning("\n❌ Errors encountered:")
            for error in self.stats['errors']:
                logger.warning(f"  - {error}")
        
        if self.dry_run:
            logger.info("\n🔍 This was a DRY RUN - no changes were made")
        else:
            logger.info("\n✅ Migration completed successfully!")
            logger.info("📝 Next steps:")
            logger.info("  1. Use the hierarchical scraping API to re-scrape stories")
            logger.info("  2. Test the new Story → Page → Chapter workflow")
            logger.info("  3. Update frontend to use hierarchical endpoints")


async def main():
    """Main migration function"""
    parser = argparse.ArgumentParser(description="Migrate to hierarchical database structure")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--no-backup", action="store_true", help="Skip creating backup")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Initialize database
    await db_manager.connect()
    
    try:
        migration = HierarchicalMigration(
            dry_run=args.dry_run,
            backup=not args.no_backup
        )
        await migration.run_migration()
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        sys.exit(1)
    finally:
        await db_manager.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
