"""
Data Scraping API Endpoints

This module provides endpoints for scraping Vietnamese web novels,
including story information and batch chapter content scraping.
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Optional
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Request
from fastapi.responses import JSONResponse

from API.models.schemas import (
    StoryInfoRequest, StoryInfoResponse, ChapterInfo, PageInfo,
    BatchChapterScrapeRequest, BatchChapterScrapeResponse, ChapterContent,
    APIResponse
)
from API.models.database import Story, Chapter, ScrapingJob, StoryStatus, ScrapingStatus
from API.utils.database import story_service, chapter_service, scraping_job_service
from API.services.scraping_service import ScrapingService, get_scraper
from API.middleware.error_handling import ScrapingError, DatabaseError
from API.utils.logging_config import LoggerMixin
from API.core.config import get_settings

router = APIRouter()
settings = get_settings()


class ScrapingController(LoggerMixin):
    """Controller for scraping operations"""
    
    def __init__(self):
        self.scraping_service = ScrapingService()
    
    async def scrape_story_info(
        self,
        request: StoryInfoRequest,
        request_obj: Request,
        background_tasks: BackgroundTasks
    ) -> StoryInfoResponse:
        """Scrape comprehensive story information and chapter list with page organization"""
        try:
            self.log_info(f"🚀 Starting comprehensive story info scraping for: {request.story_url}")

            # Check if story already exists
            existing_story = await story_service.find_by_url(str(request.story_url))

            # Scrape comprehensive story information using refactored scraper
            story_data = await self.scraping_service.scrape_story_info(
                story_url=str(request.story_url),
                request=request_obj,
                max_pages=request.max_pages
            )
            
            # Debug: Log the type and content of story_data
            self.log_info(f"📊 story_data type: {type(story_data)}")
            self.log_info(f"📊 story_data content: {story_data}")
            
            # Validate that story_data is a dictionary
            if not isinstance(story_data, dict):
                self.log_error(f"❌ Expected dict but got {type(story_data)}: {story_data}")
                raise ScrapingError(f"Invalid story data format: expected dict, got {type(story_data)}")

            # Create or update story in database
            story_id = await self._save_story_data(story_data, existing_story)

            # Create scraping job for chapters (always included now)
            job_id = None
            chapters = story_data.get("chapters", [])
            
            # Validate chapters data - ensure it's a list of dictionaries
            if chapters and isinstance(chapters, list) and all(isinstance(ch, dict) and 'url' in ch for ch in chapters):
                job_id = await self._create_scraping_job(
                    story_id,
                    chapters,
                    "story_info"
                )
            elif chapters and not isinstance(chapters, list):
                self.log_warning(f"⚠️ Chapters data is not a list: {type(chapters)} - {chapters}")
                chapters = []  # Reset to empty list to avoid errors below

                # Start background scraping
                background_tasks.add_task(
                    self._background_chapter_scraping,
                    job_id,
                    chapters
                )

            # Prepare chapter information with page numbers
            chapter_info_list = []
            for chapter in chapters:
                chapter_info_list.append(ChapterInfo(
                    chapter_number=chapter.get("chapter_number", len(chapter_info_list) + 1),
                    title=chapter.get("title", f"Chapter {len(chapter_info_list) + 1}"),
                    url=chapter["url"],
                    page_number=chapter.get("page_number", 1)
                ))

            # Prepare page information
            pages = []
            for page_data in story_data.get("pages", []):
                page_chapters = []
                for chapter in page_data.get("chapters", []):
                    page_chapters.append(ChapterInfo(
                        chapter_number=chapter.get("chapter_number", len(page_chapters) + 1),
                        title=chapter.get("title", f"Chapter {len(page_chapters) + 1}"),
                        url=chapter["url"],
                        page_number=page_data["page_number"]
                    ))

                pages.append(PageInfo(
                    page_number=page_data["page_number"],
                    url=page_data["url"],
                    chapters=page_chapters
                ))

            # Get metadata properly
            metadata = story_data.get("metadata", {})

            return StoryInfoResponse(
                success=True,
                message="Story information scraped successfully with comprehensive page organization",
                story_id=str(story_id),
                title=story_data["title"],
                author=metadata.get("author", "Unknown"),
                description=metadata.get("description", ""),
                cover_image_url=metadata.get("cover_image_url"),
                status=StoryStatus(metadata.get("status", "ongoing")),
                total_chapters=metadata.get("total_chapters", len(chapter_info_list)),
                total_pages=metadata.get("total_pages", 1),
                pages=pages,
                scraping_job_id=str(job_id) if job_id else None
            )

        except Exception as e:
            self.log_error(f"Error scraping comprehensive story info: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}")
    
    async def batch_scrape_chapters(
        self,
        request: BatchChapterScrapeRequest,
        background_tasks: BackgroundTasks
    ) -> BatchChapterScrapeResponse:
        """Scrape multiple chapters in batch"""
        try:
            self.log_info(f"Starting batch chapter scraping for {len(request.chapter_urls)} chapters")
            
            # Validate URLs
            chapter_urls = [str(url) for url in request.chapter_urls]
            
            # Get or create story
            story_id = None
            if request.story_id:
                story = await story_service.find_by_id(request.story_id)
                if story:
                    story_id = story["_id"]
            
            # Create scraping job
            job_data = {
                "story_id": story_id,
                "job_type": "batch_chapters",
                "total_items": len(chapter_urls),
                "chapter_urls": chapter_urls,
                "max_concurrent": request.max_concurrent,
                "rate_limit_delay": request.rate_limit_delay
            }
            
            job_id = await scraping_job_service.create_job(job_data)
            
            # Start background scraping
            background_tasks.add_task(
                self._background_batch_scraping,
                job_id,
                chapter_urls,
                request.max_concurrent,
                request.rate_limit_delay
            )
            
            return BatchChapterScrapeResponse(
                success=True,
                message="Batch chapter scraping started",
                job_id=str(job_id),
                total_chapters=len(chapter_urls),
                chapters=[],  # Will be populated as scraping progresses
                failed_urls=[]
            )
            
        except Exception as e:
            self.log_error(f"Error starting batch chapter scraping: {e}")
            raise ScrapingError(f"Failed to start batch scraping: {e}")
    
    async def _save_story_data(
        self,
        story_data: dict,
        existing_story: Optional[dict] = None
    ) -> str:
        """Save or update story data in database with proper metadata structure"""
        try:
            # Debug: Log the story_data structure
            self.log_info(f"📝 Saving story data - Title: {story_data.get('title')}")
            self.log_info(f"📝 Story data keys: {list(story_data.keys())}")
            self.log_info(f"📝 Metadata keys: {list(story_data.get('metadata', {}).keys())}")

            # Ensure we have a title
            title = story_data.get("title")
            if not title:
                raise ValueError("Story title is required but not found in story_data")

            # Generate slug from title
            slug = self._generate_slug(title)

            # Extract metadata properly from story_data
            scraper_metadata = story_data.get("metadata", {})

            # Create proper StoryMetadata structure according to database schema
            story_metadata = {
                "author": scraper_metadata.get("author", "Unknown"),
                "description": scraper_metadata.get("description", ""),
                "genres": [],  # Can be expanded later
                "tags": [],    # Can be expanded later
                "cover_image_url": scraper_metadata.get("cover_image_url"),
                "status": scraper_metadata.get("status", "ongoing"),
                "total_chapters": story_data.get("total_chapters", len(story_data.get("chapters", []))),
                "scraped_at": datetime.now(timezone.utc),
                "scraping_duration": None,  # Can be calculated if needed
                "source_website": scraper_metadata.get("source_website", "webtruyen"),
                "scraping_errors": []
            }

            # Main story document structure
            story_doc = {
                "title": title,
                "url": story_data["url"],
                "slug": slug,
                "total_chapters_scraped": 0,  # Will be updated as chapters are scraped
                "total_chapters_enhanced": 0,
                "scraping_status": ScrapingStatus.IN_PROGRESS,
                "enhancement_progress": 0.0,
                "metadata": story_metadata,
                "updated_at": datetime.now(timezone.utc),
                "last_scraped_at": datetime.now(timezone.utc)
            }

            self.log_info(f"📝 Prepared story document with metadata: author='{story_metadata['author']}', "
                         f"description_length={len(story_metadata.get('description', ''))}, "
                         f"cover_image={'present' if story_metadata.get('cover_image_url') else 'missing'}")

            if existing_story:
                # Update existing story
                await story_service.update_by_id(existing_story["_id"], {"$set": story_doc})
                self.log_info(f"✅ Updated existing story: {title}")
                return str(existing_story["_id"])
            else:
                # Create new story
                story_doc["created_at"] = datetime.now(timezone.utc)
                story_id = await story_service.insert_one(story_doc)
                self.log_info(f"✅ Created new story: {title}")
                return str(story_id)

        except Exception as e:
            self.log_error(f"Error saving story data: {e}")
            raise DatabaseError(f"Failed to save story data: {e}")
    
    async def _create_scraping_job(
        self,
        story_id: str,
        chapters: List[dict],
        job_type: str
    ) -> str:
        """Create scraping job for chapters"""
        try:
            from bson import ObjectId
            
            # Debug: Log the chapters data
            self.log_info(f"📊 chapters type: {type(chapters)}")
            self.log_info(f"📊 chapters length: {len(chapters) if chapters else 0}")
            if chapters:
                self.log_info(f"📊 first chapter type: {type(chapters[0])}")
                self.log_info(f"📊 first chapter content: {chapters[0]}")
            
            chapter_urls = [chapter["url"] for chapter in chapters]

            job_data = {
                "story_id": ObjectId(story_id),
                "job_type": job_type,
                "total_items": len(chapters),
                "chapter_urls": chapter_urls,
                "max_concurrent": settings.max_concurrent_scraping,
                "rate_limit_delay": settings.scraping_delay_min
            }
            
            job_id = await scraping_job_service.create_job(job_data)
            return str(job_id)
            
        except Exception as e:
            self.log_error(f"Error creating scraping job: {e}")
            raise DatabaseError(f"Failed to create scraping job: {e}")
    
    async def _background_chapter_scraping(
        self,
        job_id: str,
        chapters: List[dict]
    ):
        """Background task for scraping chapters"""
        try:
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=0,
                status="in_progress"
            )
            
            completed = 0
            failed = 0
            errors = []
            
            # Process chapters with concurrency control
            semaphore = asyncio.Semaphore(settings.max_concurrent_scraping)
            
            async def scrape_single_chapter(chapter_data):
                nonlocal completed, failed, errors
                
                async with semaphore:
                    try:
                        # Add delay for rate limiting
                        await asyncio.sleep(settings.scraping_delay_min)
                        
                        # Scrape chapter content
                        content_data = await self.scraping_service.scrape_chapter_content(
                            chapter_data["url"]
                        )
                        
                        # Save chapter to database
                        await self._save_chapter_data(content_data, job_id)
                        
                        completed += 1
                        self.log_info(f"Successfully scraped chapter: {chapter_data['title']}")
                        
                    except Exception as e:
                        failed += 1
                        error_msg = f"Failed to scrape {chapter_data['url']}: {e}"
                        errors.append(error_msg)
                        self.log_error(error_msg)
                    
                    # Update job progress
                    await scraping_job_service.update_job_progress(
                        job_id,
                        completed_items=completed,
                        failed_items=failed,
                        errors=errors[-10:]  # Keep only last 10 errors
                    )
            
            # Execute scraping tasks
            tasks = [scrape_single_chapter(chapter) for chapter in chapters]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Mark job as completed
            final_status = "completed" if failed == 0 else "completed_with_errors"
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status=final_status,
                errors=errors
            )
            
            self.log_info(f"Chapter scraping job {job_id} completed: {completed} success, {failed} failed")
            
        except Exception as e:
            self.log_error(f"Error in background chapter scraping: {e}")
            await scraping_job_service.update_job_progress(
                job_id,
                completed_items=completed,
                failed_items=failed,
                status="failed",
                errors=[str(e)]
            )
    
    async def _background_batch_scraping(
        self,
        job_id: str,
        chapter_urls: List[str],
        max_concurrent: int,
        rate_limit_delay: float
    ):
        """Background task for batch chapter scraping"""
        # Similar implementation to _background_chapter_scraping
        # but with different parameters and URL handling
        pass  # Implementation would be similar to above
    
    async def _save_chapter_data(self, content_data: dict, job_id: str):
        """Save chapter content data to database"""
        try:
            # Clean content_data to ensure no unwanted fields like _id
            cleaned_content_data = {
                "url": content_data.get("url", ""),
                "title": content_data.get("title", ""),
                "content": content_data.get("content", ""),
                "is_locked": content_data.get("is_locked", False),
                "scraping_duration": content_data.get("scraping_duration"),
                "timestamp": content_data.get("timestamp"),
                "metadata": content_data.get("metadata", {})
            }

            # Get story_id from the job
            job_data = await scraping_job_service.find_by_id(job_id)
            if not job_data:
                raise DatabaseError(f"Job not found: {job_id}")

            story_id = job_data.get("story_id")

            # Extract chapter number from URL or title
            chapter_number = self._extract_chapter_number(
                cleaned_content_data.get("url", ""),
                cleaned_content_data.get("title", "")
            )

            chapter_doc = {
                "story_id": story_id,  # Can be None for standalone chapters
                "chapter_number": chapter_number,
                "title": cleaned_content_data["title"],
                "url": cleaned_content_data["url"],
                "original_content": cleaned_content_data["content"],
                "is_scraped": True,
                "is_enhanced": False,
                "metadata": {
                    "word_count": len(cleaned_content_data["content"].split()) if cleaned_content_data.get("content") else 0,
                    "character_count": len(cleaned_content_data["content"]) if cleaned_content_data.get("content") else 0,
                    "scraped_at": datetime.now(timezone.utc),
                    "scraping_duration": cleaned_content_data.get("scraping_duration"),
                    "source_url": cleaned_content_data["url"],
                    "is_locked": cleaned_content_data.get("is_locked", False),
                    "scraping_job_id": job_id
                }
            }
            
            # Check if chapter already exists (by story_id + chapter_number or URL)
            existing_chapter = None

            # First try to find by story_id and chapter_number (more reliable)
            if story_id and chapter_number:
                existing_chapter = await chapter_service.find_one({
                    "story_id": story_id,
                    "chapter_number": chapter_number
                })

            # Fallback: find by URL
            if not existing_chapter:
                existing_chapter = await chapter_service.find_by_url(cleaned_content_data["url"])

            if existing_chapter:
                # Update existing chapter - ensure no _id field in update document
                update_doc = {k: v for k, v in chapter_doc.items() if k != "_id"}
                await chapter_service.update_by_id(existing_chapter["_id"], {"$set": update_doc})
                self.log_info(f"Updated existing chapter: {cleaned_content_data['title']}")
            else:
                # Insert new chapter with duplicate key error handling
                try:
                    await chapter_service.insert_one(chapter_doc)
                    self.log_info(f"Inserted new chapter: {cleaned_content_data['title']}")
                except Exception as insert_error:
                    # Handle duplicate key error gracefully
                    if "duplicate key error" in str(insert_error).lower():
                        self.log_warning(f"Chapter already exists (duplicate key): {cleaned_content_data['title']}")
                        # Try to update instead
                        try:
                            existing_chapter = await chapter_service.find_one({
                                "story_id": story_id,
                                "chapter_number": chapter_number
                            })
                            if existing_chapter:
                                # Update existing chapter - ensure no _id field in update document
                                update_doc = {k: v for k, v in chapter_doc.items() if k != "_id"}
                                await chapter_service.update_by_id(existing_chapter["_id"], {"$set": update_doc})
                                self.log_info(f"Updated chapter after duplicate key error: {cleaned_content_data['title']}")
                        except Exception as update_error:
                            self.log_error(f"Failed to update after duplicate key error: {update_error}")
                            raise
                    else:
                        raise insert_error
                
        except Exception as e:
            self.log_error(f"Error saving chapter data: {e}")
            raise DatabaseError(f"Failed to save chapter data: {e}")
    
    def _generate_slug(self, title: str) -> str:
        """Generate URL-friendly slug from title"""
        import re
        slug = re.sub(r'[^\w\s-]', '', title.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def _extract_chapter_number(self, url: str, title: str) -> int:
        """Extract chapter number from URL or title"""
        import re
        
        # Try to extract from URL first
        url_match = re.search(r'chuong-(\d+)', url)
        if url_match:
            return int(url_match.group(1))
        
        # Try to extract from title
        title_match = re.search(r'chương\s*(\d+)', title.lower())
        if title_match:
            return int(title_match.group(1))
        
        # Default to 1 if not found
        return 1


# ============================================================================
# Router Endpoints
# ============================================================================

controller = ScrapingController()


@router.post("/story-info", response_model=StoryInfoResponse)
async def scrape_story_info(
    request: StoryInfoRequest,
    request_obj: Request,
    background_tasks: BackgroundTasks
):
    """
    Scrape comprehensive story information and chapter list with page organization

    This endpoint scrapes comprehensive story information including:
    - Story details (title, author, description, cover image)
    - Complete chapter list organized by pagination pages
    - Page information with URLs and chapter metadata for future content scraping
    - Total chapter count and pagination metadata

    The response includes both a flat chapter list and organized page information
    to support different use cases for batch processing and content scraping.
    """
    return await controller.scrape_story_info(request, request_obj, background_tasks)


@router.post("/batch-chapters", response_model=BatchChapterScrapeResponse)
async def batch_scrape_chapters(
    request: BatchChapterScrapeRequest,
    background_tasks: BackgroundTasks
):
    """
    Scrape multiple chapters in batch
    
    This endpoint accepts an array of chapter URLs and scrapes their content
    concurrently with configurable rate limiting and concurrency controls.
    """
    return await controller.batch_scrape_chapters(request, background_tasks)


@router.get("/test-scraping")
async def test_scraping(request: Request):
    """Test endpoint to verify scraping functionality"""
    try:
        # Test if we can get the scraper from the app state
        scraper = await get_scraper(request)
        
        if scraper:
            status = "Scraping service connected and ready"
        else:
            status = "Scraping service initialization failed"
        
        return APIResponse(
            success=True,
            message=f"Scraping service status: {status}"
        )
        
    except Exception as e:
        return APIResponse(
            success=True,
            message=f"Scraping service status: Connection test failed: {e}"
        )
