/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Configure external image domains
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'static.cuatui.us',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'webtruyen.diendantruyen.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**', // Allow all HTTPS domains (use with caution)
      },
    ],
    // Optimize images
    formats: ['image/webp', 'image/avif'],
    // Set reasonable size limits
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Enable placeholder blur for better UX
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
};

export default nextConfig;