import os
import sys
from functools import lru_cache
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
from bson import ObjectId

# Add the parent directory to the Python path to allow for module imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now, you can import the config
from config import Settings

# ====================================================================
# Configuration
# ====================================================================

@lru_cache
def get_settings():
    return Settings()

settings = get_settings()

MONGODB_URL = settings.mongodb_url
DB_NAME = settings.mongodb_database

# ====================================================================
# Main Script
# ====================================================================

def check_database_connection():
    """Connects to MongoDB and verifies data in collections."""
    print(f"Attempting to connect to MongoDB at {MONGODB_URL}...")
    
    try:
        client = MongoClient(MONGODB_URL, serverSelectionTimeoutMS=5000)
        # The ismaster command is cheap and does not require auth.
        client.admin.command('ismaster')
        print("MongoDB connection successful.")
    except ConnectionFailure as e:
        print(f"MongoDB connection failed: {e}")
        return

    try:
        db = client[DB_NAME]
        print(f"Successfully connected to database: '{DB_NAME}'")
        
        # List all collections in the database
        collections = db.list_collection_names()
        print(f"\nCollections in '{DB_NAME}': {collections}")
        
        # 1. Check 'stories' collection
        if 'stories' in collections:
            stories_collection = db.stories
            stories_count = stories_collection.count_documents({})
            print(f"\nFound {stories_count} document(s) in 'stories' collection.")
            if stories_count > 0:
                print("Sample story:")
                sample_story = stories_collection.find_one()
                print(sample_story)
        else:
            print("\n'stories' collection not found.")

        # 2. Check 'chapters' collection
        if 'chapters' in collections:
            chapters_collection = db.chapters
            chapters_count = chapters_collection.count_documents({})
            print(f"\nFound {chapters_count} document(s) in 'chapters' collection.")
            if chapters_count > 0:
                print("Sample chapter:")
                sample_chapter = chapters_collection.find_one()
                print(sample_chapter)
        else:
            print("\n'chapters' collection not found.")
            
    except Exception as e:
        print(f"An error occurred while checking the database: {e}")
    finally:
        client.close()
        print("\nMongoDB connection closed.")

if __name__ == "__main__":
    check_database_connection()