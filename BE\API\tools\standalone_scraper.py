#!/usr/bin/env python3
"""
Standalone scraper script that runs in a separate process to avoid event loop conflicts.
"""

import asyncio
import sys
import json
from pathlib import Path

# Add scraper to path
scraper_path = Path(__file__).parent.parent.parent / "Scraper" / "src"
if str(scraper_path) not in sys.path:
    sys.path.insert(0, str(scraper_path))

from metruyenscraper import MetruyenScraper

async def scrape_chapter_content(chapter_url: str, config_path: str) -> dict:
    """
    Scrape chapter content using MetruyenScraper in a clean event loop.
    
    Args:
        chapter_url: URL of the chapter to scrape
        config_path: Path to the scraper configuration file
        
    Returns:
        Dictionary containing scraped content
    """
    try:
        # Initialize scraper
        scraper = MetruyenScraper(config_path)
        
        async with scraper:
            # Access the scraper engine directly for more control
            engine = scraper.scraper_engine
            browser_manager = engine.browser_manager
            
            # Navigate to the chapter URL
            page = await browser_manager.new_page()
            await page.goto(chapter_url, wait_until="domcontentloaded")
            
            # Extract content using the configured selectors
            config = scraper.config.get_target_config("webtruyen")
            selectors = config.get("selectors", {})
            
            # Extract title
            title = ""
            title_selector = selectors.get("title", "h1, .entry-title, .chapter-title, h3")
            title_element = await page.query_selector(title_selector)
            if title_element:
                title = await title_element.inner_text()
                title = title.strip()
            
            # Extract content
            content = ""
            content_selector = selectors.get("content", ".chapter-content, .story-content, .content-area p")
            content_elements = await page.query_selector_all(content_selector)
            
            if content_elements:
                content_parts = []
                for element in content_elements:
                    text = await element.inner_text()
                    if text.strip():
                        content_parts.append(text.strip())
                content = "\n\n".join(content_parts)
            
            if content or title:
                return {
                    "success": True,
                    "content": content,
                    "title": title,
                    "url": chapter_url
                }
            else:
                return {
                    "success": False,
                    "error": "No content or title found",
                    "url": chapter_url
                }
                
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": chapter_url
        }

def main():
    if len(sys.argv) != 3:
        print(json.dumps({"success": False, "error": "Usage: standalone_scraper.py <chapter_url> <config_path>"}))
        sys.exit(1)
    
    chapter_url = sys.argv[1]
    config_path = sys.argv[2]
    
    # Set Windows event loop policy
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Run scraping
    result = asyncio.run(scrape_chapter_content(chapter_url, config_path))
    
    # Output result as JSON
    print(json.dumps(result))

if __name__ == "__main__":
    main()