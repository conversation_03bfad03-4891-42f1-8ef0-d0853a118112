'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import { Chapter } from '@/types/story';
import { useChaptersCache } from '@/hooks/useChaptersCache';

export interface ChapterFilter {
  scraped?: boolean | null;
  enhanced?: boolean | null;
  search?: string;
}

export const useChapterManagement = (storyId: string, propChapters?: Chapter[]) => {
  const { 
    chapters: cachedChapters, 
    loading: chaptersLoading, 
    totalChapters,
    currentPage: cachePage,
    totalPages: cachedTotalPages,
    loadChapters 
  } = useChaptersCache(storyId);

  const chapters = propChapters || cachedChapters;

  const [selectedChapters, setSelectedChapters] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<ChapterFilter>({});
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (!propChapters && !chaptersLoading) {
      loadChapters(currentPage, itemsPerPage);
    }
  }, [propChapters, chaptersLoading, currentPage, itemsPerPage, loadChapters]);

  const filteredChapters = useMemo(() => {
    if (!chapters || chapters.length === 0) return [];
    
    let filtered = chapters;
    
    if (filter.scraped !== null && filter.scraped !== undefined) {
      filtered = filtered.filter(chapter => chapter.is_scraped === filter.scraped);
    }

    if (filter.enhanced !== null && filter.enhanced !== undefined) {
      filtered = filtered.filter(chapter => chapter.is_enhanced === filter.enhanced);
    }

    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(chapter =>
        (chapter.title && chapter.title.toLowerCase().includes(searchTerm)) ||
        chapter.chapter_number.toString().includes(searchTerm)
      );
    }

    return filtered.sort((a, b) => a.chapter_number - b.chapter_number);
  }, [chapters, filter]);

  const paginatedChapters = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredChapters.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredChapters, currentPage, itemsPerPage]);

  const totalPages = propChapters ? Math.ceil(filteredChapters.length / itemsPerPage) : cachedTotalPages;
  const effectiveCurrentPage = propChapters ? currentPage : cachePage;

  const chapterStats = useMemo(() => {
    const total = filteredChapters.length;
    const scraped = filteredChapters.filter(ch => ch.is_scraped).length;
    const enhanced = filteredChapters.filter(ch => ch.is_enhanced).length;
    const unscraped = total - scraped;
    const scrapedNotEnhanced = scraped - enhanced;
    const selected = selectedChapters.size;
    
    return { total, scraped, enhanced, unscraped, scrapedNotEnhanced, selected };
  }, [filteredChapters, selectedChapters]);

  useEffect(() => {
    setSelectedChapters(new Set());
    setCurrentPage(1);
  }, [filter]);

  const handleSelectChapter = useCallback((chapterId: string) => {
    setSelectedChapters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(chapterId)) {
        newSet.delete(chapterId);
      } else {
        newSet.add(chapterId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((mode: 'scraping' | 'enhancement') => {
    let selectableChapters: string[] = [];
    
    if (mode === 'scraping') {
      selectableChapters = paginatedChapters
        .filter(ch => !ch.is_scraped)
        .map(ch => ch.id);
    } else if (mode === 'enhancement') {
      selectableChapters = paginatedChapters
        .filter(ch => ch.is_scraped && !ch.is_enhanced)
        .map(ch => ch.id);
    }
    
    setSelectedChapters(new Set(selectableChapters));
  }, [paginatedChapters]);

  const handleDeselectAll = useCallback(() => {
    setSelectedChapters(new Set());
  }, []);

  return {
    chapters,
    chaptersLoading,
    totalChapters,
    filteredChapters,
    paginatedChapters,
    currentPage: effectiveCurrentPage,
    totalPages,
    itemsPerPage,
    setCurrentPage,
    setItemsPerPage,
    filter,
    setFilter,
    selectedChapters,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
    chapterStats,
    setSelectedChapters
  };
};
