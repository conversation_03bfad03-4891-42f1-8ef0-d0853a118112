'use client';

import { useState, useMemo, useEffect, useCallback } from 'react';
import { Chapter } from '@/types/story';
import { useChaptersCache } from '@/hooks/useChaptersCache';

export interface ChapterFilter {
  scraped?: boolean | null;
  enhanced?: boolean | null;
  search?: string;
}

export const useChapterManagement = (storyId: string) => {
  const {
    chapters,
    loading: chaptersLoading,
    totalChapters,
    currentPage,
    totalPages,
    pageSize,
    setPageSize,
    goToPage,
  } = useChaptersCache(storyId);

  const [selectedChapters, setSelectedChapters] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<ChapterFilter>({ scraped: null, enhanced: null });

  // Load chapters with filters when filter changes (excluding search)
  useEffect(() => {
    const apiFilters = {
      enhanced_only: filter.enhanced === true ? true : undefined,
      scraped_only: filter.scraped === true ? true : undefined
    };
    goToPage(1, apiFilters);
  }, [filter.scraped, filter.enhanced, storyId]);

  const filteredChapters = useMemo(() => {
    if (!chapters || chapters.length === 0) return [];
    
    let filtered = chapters;
    
    // Client-side search filtering (only for search, other filters are handled server-side)
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(chapter =>
        (chapter.title && chapter.title.toLowerCase().includes(searchTerm)) ||
        chapter.chapter_number.toString().includes(searchTerm)
      );
    }

    return filtered.sort((a, b) => a.chapter_number - b.chapter_number);
  }, [chapters, filter.search]);

  // Use server-side pagination for main chapters, client-side only for search filtering
  const displayChapters = filter.search ? filteredChapters : chapters;
  const displayTotalPages = filter.search ? Math.ceil(filteredChapters.length / pageSize) : totalPages;

  const chapterStats = useMemo(() => {
    const total = filter.search ? filteredChapters.length : totalChapters;
    const scraped = displayChapters.filter(ch => ch.is_scraped).length;
    const enhanced = displayChapters.filter(ch => ch.is_enhanced).length;
    const unscraped = total - scraped;
    const scrapedNotEnhanced = scraped - enhanced;
    const selected = selectedChapters.size;
    
    return { total, scraped, enhanced, unscraped, scrapedNotEnhanced, selected };
  }, [displayChapters, filteredChapters.length, totalChapters, filter.search, selectedChapters]);

  // Reset selections when filter changes
  useEffect(() => {
    setSelectedChapters(new Set());
  }, [filter]);

  const handleSelectChapter = useCallback((chapterId: string) => {
    setSelectedChapters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(chapterId)) {
        newSet.delete(chapterId);
      } else {
        newSet.add(chapterId);
      }
      return newSet;
    });
  }, []);

  const handleSelectAll = useCallback((mode: 'scraping' | 'enhancement') => {
    let selectableChapters: string[] = [];
    
    if (mode === 'scraping') {
      selectableChapters = displayChapters
        .filter(ch => !ch.is_scraped)
        .map(ch => ch.id);
    } else if (mode === 'enhancement') {
      selectableChapters = displayChapters
        .filter(ch => ch.is_scraped && !ch.is_enhanced)
        .map(ch => ch.id);
    }
    
    setSelectedChapters(new Set(selectableChapters));
  }, [displayChapters]);

  const handleDeselectAll = useCallback(() => {
    setSelectedChapters(new Set());
  }, []);

  // Handle page change with current filters
  const handlePageChange = useCallback((page: number) => {
    const apiFilters = {
      enhanced_only: filter.enhanced === true ? true : undefined,
      scraped_only: filter.scraped === true ? true : undefined
    };
    goToPage(page, apiFilters);
  }, [goToPage, filter.enhanced, filter.scraped]);

  // Handle page size change
  const handlePageSizeChange = useCallback((size: number) => {
    console.log('handlePageSizeChange', size);
    setPageSize(size);
    goToPage(1, {
      enhanced_only: filter.enhanced === true ? true : undefined,
      scraped_only: filter.scraped === true ? true : undefined
    }, size);
  }, [goToPage, filter.enhanced, filter.scraped, setPageSize]);

  return {
    chapters: displayChapters,
    chaptersLoading,
    totalChapters,
    filteredChapters,
    currentPage,
    totalPages: displayTotalPages,
    pageSize,
    setPageSize,
    filter,
    setFilter,
    selectedChapters,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
    handlePageChange,
    handlePageSizeChange,
    chapterStats,
  };
};
