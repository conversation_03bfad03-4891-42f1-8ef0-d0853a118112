#!/usr/bin/env python3
"""
JSON to Files Converter

This script reads JSON files containing story chapter data,
then creates individual Markdown files for each chapter with proper Vietnamese text support.

Requirements:
- JSON file must be an array of chapter objects with configurable field names
- Handles Vietnamese text encoding properly
- Sanitizes filenames for cross-platform compatibility
- <PERSON><PERSON> duplicate chapter names
- Optional AI enhancement for Markdown files
"""

import os
import re
import sys
import json
import unicodedata
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union
import click
from tqdm import tqdm

# Import AI enhancer from parent directory
sys.path.append(str(Path(__file__).parent.parent))
from ai_enhancer import GeminiTextEnhancer


class JsonToFilesConverter:
    """Main converter class for processing JSON files to individual Markdown files."""

    def __init__(self,
                 output_dir: str = "output_json",
                 file_extension: str = ".md",
                 enable_ai: bool = False,
                 title_field: str = "title",
                 content_field: str = "content"):
        """
        Initialize the converter.

        Args:
            output_dir: Directory to save the generated files
            file_extension: File extension for output files (.txt or .md)
            enable_ai: Whether to enable AI text enhancement for .md files
            title_field: JSON field name for chapter titles
            content_field: JSON field name for chapter content
        """
        self.output_dir = Path(output_dir)
        self.file_extension = file_extension
        self.chapter_counts: Dict[str, int] = {}
        self.enable_ai = enable_ai
        self.ai_enhancer = None
        self.title_field = title_field
        self.content_field = content_field

        # Initialize AI enhancer if requested and format is markdown
        if self.enable_ai and self.file_extension == ".md":
            try:
                self.ai_enhancer = GeminiTextEnhancer()
                if self.ai_enhancer.is_available():
                    print("🤖 AI text enhancement enabled for Markdown files")
                    logging.info("AI enhancer initialized successfully")
                else:
                    print("⚠️  AI enhancer not available, proceeding without AI enhancement")
                    self.ai_enhancer = None
            except Exception as e:
                print(f"⚠️  Failed to initialize AI enhancer: {e}")
                print("   Proceeding without AI enhancement")
                self.ai_enhancer = None
                logging.error(f"AI enhancer initialization failed: {e}")

    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize a string to be safe for use as a filename.

        Args:
            filename: The original filename string

        Returns:
            A sanitized filename safe for cross-platform use
        """
        # First, normalize whitespace and remove control characters
        # Replace tabs, newlines, and other whitespace with single spaces
        sanitized = re.sub(r'\s+', ' ', filename)

        # Remove control characters (ASCII 0-31 and 127)
        sanitized = re.sub(r'[\x00-\x1f\x7f]', '', sanitized)

        # Remove or replace invalid characters for filenames
        # Invalid characters: < > : " | ? * \ /
        invalid_chars = r'[<>:"|?*\\/]'
        sanitized = re.sub(invalid_chars, '_', sanitized)

        # Remove leading/trailing whitespace and dots
        sanitized = sanitized.strip(' .')

        # Replace multiple consecutive underscores with single underscore
        sanitized = re.sub(r'_+', '_', sanitized)

        # Limit length to avoid filesystem issues (255 chars is common limit)
        max_length = 200  # Leave room for extension and potential numbering
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length].strip()

        # Ensure we don't have an empty filename
        if not sanitized:
            sanitized = "untitled"

        return sanitized

    def handle_duplicate_filename(self, base_filename: str) -> str:
        """
        Handle duplicate filenames by appending a number.

        Args:
            base_filename: The base filename (without extension)

        Returns:
            A unique filename
        """
        if base_filename not in self.chapter_counts:
            self.chapter_counts[base_filename] = 1
            return base_filename
        else:
            self.chapter_counts[base_filename] += 1
            return f"{base_filename}_{self.chapter_counts[base_filename]}"

    def read_json_file(self, json_path: str) -> List[Dict[str, Any]]:
        """
        Read the JSON file and validate required fields.

        Args:
            json_path: Path to the JSON file

        Returns:
            List of chapter dictionaries

        Raises:
            FileNotFoundError: If the JSON file doesn't exist
            ValueError: If JSON is malformed or required fields are missing
        """
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"JSON file not found: {json_path}")

        try:
            # Read JSON file with UTF-8 encoding
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error reading JSON file: {str(e)}")

        # Ensure data is a list
        if not isinstance(data, list):
            raise ValueError("JSON file must contain an array of chapter objects")

        if not data:
            raise ValueError("JSON file contains no chapter data")

        # Validate that chapters have required fields
        missing_fields_chapters = []
        for i, chapter in enumerate(data):
            if not isinstance(chapter, dict):
                raise ValueError(f"Chapter {i + 1} is not a valid object")

            missing_fields = []
            if self.title_field not in chapter:
                missing_fields.append(self.title_field)
            if self.content_field not in chapter:
                missing_fields.append(self.content_field)

            if missing_fields:
                missing_fields_chapters.append({
                    'chapter_index': i + 1,
                    'missing_fields': missing_fields
                })

        if missing_fields_chapters:
            error_details = []
            for chapter_info in missing_fields_chapters[:5]:  # Show first 5 errors
                error_details.append(
                    f"Chapter {chapter_info['chapter_index']}: missing {', '.join(chapter_info['missing_fields'])}"
                )

            if len(missing_fields_chapters) > 5:
                error_details.append(f"... and {len(missing_fields_chapters) - 5} more chapters with missing fields")

            raise ValueError(
                f"Missing required fields in chapters:\n" + "\n".join(error_details) +
                f"\nRequired fields: '{self.title_field}', '{self.content_field}'"
            )

        return data

    def generate_filename(self, chapter: Dict[str, Any], index: int) -> str:
        """
        Generate a meaningful filename from chapter data.

        Args:
            chapter: Chapter dictionary
            index: Chapter index (0-based)

        Returns:
            Generated filename (without extension)
        """
        title = str(chapter.get(self.title_field, '')).strip()

        # If title is empty or just whitespace, use index-based naming
        if not title or title == 'nan':
            return f"chapter_{index + 1:03d}"

        # Use title as base filename
        base_filename = self.sanitize_filename(title)

        # If sanitization resulted in empty string, fall back to index
        if not base_filename or base_filename == "untitled":
            return f"chapter_{index + 1:03d}"

        return base_filename

    def write_chapter_file(self, chapter_title: str, chapter_content: str, filename: str, ai_enhanced: bool = False) -> None:
        """
        Write a chapter to a file with proper encoding.

        Args:
            chapter_title: The chapter title/name
            chapter_content: The chapter content
            filename: The filename to write to
            ai_enhanced: Whether the content was enhanced by AI
        """
        try:
            file_path = self.output_dir / f"{filename}{self.file_extension}"

            # Ensure the directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Prepare the content
            if self.file_extension == ".md":
                # Markdown format
                content = f"# {chapter_title}\n\n{chapter_content}"
            else:
                # Plain text format
                content = f"{chapter_title}\n{'=' * len(chapter_title)}\n\n{chapter_content}"

            # Write file with UTF-8 encoding to support Vietnamese text
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Show appropriate success message
            if ai_enhanced:
                print(f"✓ Created (AI Enhanced): {file_path}")
            else:
                print(f"✓ Created: {file_path}")

        except OSError as e:
            print(f"✗ Error writing file (invalid path/filename): {filename}")
            print(f"  Original chapter title: {chapter_title[:100]}...")
            print(f"  Error details: {str(e)}")
        except Exception as e:
            print(f"✗ Error writing {filename}: {str(e)}")

    def convert(self, json_path: str) -> None:
        """
        Main conversion method.

        Args:
            json_path: Path to the JSON file to convert
        """
        print(f"Starting conversion of: {json_path}")
        print(f"Output directory: {self.output_dir}")
        print(f"File extension: {self.file_extension}")
        print(f"Title field: '{self.title_field}'")
        print(f"Content field: '{self.content_field}'")
        if self.ai_enhancer:
            print("🤖 AI text enhancement: ENABLED")
        print("-" * 50)

        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Read JSON file
        try:
            chapters = self.read_json_file(json_path)
            print(f"Successfully read JSON file with {len(chapters)} chapters")
        except Exception as e:
            print(f"Error: {str(e)}")
            return

        # Process each chapter
        successful_conversions = 0
        ai_enhanced_count = 0
        errors = 0

        # Create progress bar if AI enhancement is enabled
        if self.ai_enhancer:
            print("\n🤖 Processing chapters with AI enhancement...")
            progress_bar = tqdm(enumerate(chapters), total=len(chapters), desc="Converting chapters", unit="chapter")
        else:
            progress_bar = enumerate(chapters)

        for index, chapter in progress_bar:
            try:
                chapter_title = str(chapter.get(self.title_field, '')).strip()
                chapter_content = str(chapter.get(self.content_field, '')).strip()

                # Check for line breaks
                line_breaks = chapter_content.count('\n')
                print(f"📈 Line breaks found: {line_breaks}")

                # Skip chapters with missing data
                if not chapter_title or chapter_title == 'nan':
                    print(f"⚠ Skipping chapter {index + 1}: Missing title")
                    continue

                if not chapter_content or chapter_content == 'nan':
                    print(f"⚠ Skipping chapter {index + 1}: Missing content")
                    continue

                # Generate filename
                base_filename = self.generate_filename(chapter, index)

                # Handle duplicates
                unique_filename = self.handle_duplicate_filename(base_filename)

                # AI Enhancement for Markdown files
                ai_enhanced = False
                final_content = chapter_content

                if self.ai_enhancer and self.file_extension == ".md":
                    try:
                        enhanced_content = self.ai_enhancer.enhance_text(chapter_title, chapter_content)
                        if enhanced_content and enhanced_content.strip():
                            final_content = enhanced_content
                            ai_enhanced = True
                            ai_enhanced_count += 1
                        else:
                            print(f"⚠️  AI enhancement failed for: {chapter_title[:50]}... (using original)")
                    except Exception as e:
                        print(f"⚠️  AI enhancement error for {chapter_title[:50]}...: {e}")
                        logging.error(f"AI enhancement failed for chapter '{chapter_title}': {e}")

                # Write the file
                self.write_chapter_file(chapter_title, final_content, unique_filename, ai_enhanced)
                successful_conversions += 1

            except Exception as e:
                print(f"✗ Error processing chapter {index + 1}: {str(e)}")
                errors += 1

        # Close progress bar if it was created
        if self.ai_enhancer and hasattr(progress_bar, 'close'):
            progress_bar.close()

        # Summary
        print("-" * 50)
        print(f"Conversion completed!")
        print(f"✓ Successfully converted: {successful_conversions} chapters")
        if self.ai_enhancer:
            print(f"🤖 AI enhanced: {ai_enhanced_count} chapters")
        if errors > 0:
            print(f"✗ Errors encountered: {errors}")
        print(f"Files saved to: {self.output_dir.absolute()}")


@click.command()
@click.argument('json_file', type=click.Path(exists=True))
@click.option('--output-dir', '-o', default='output',
              help='Output directory for generated files (default: output)')
@click.option('--format', '-f', type=click.Choice(['txt', 'md']), default='md',
              help='Output file format: txt or md (default: md)')
@click.option('--ai-enhance', '--ai', is_flag=True, default=False,
              help='Enable AI text enhancement for Markdown files (requires GOOGLE_AI_API_KEY)')
@click.option('--title-field', default='title',
              help='JSON field name for chapter titles (default: title)')
@click.option('--content-field', default='content',
              help='JSON field name for chapter content (default: content)')
@click.option('--encoding', default='utf-8',
              help='Text encoding for output files (default: utf-8)')
@click.option('--verbose', '-v', is_flag=True, default=False,
              help='Enable verbose logging')
def main(json_file: str, output_dir: str, format: str, ai_enhance: bool,
         title_field: str, content_field: str, encoding: str, verbose: bool):
    """
    Convert JSON file to individual Markdown files with optional AI enhancement.

    JSON_FILE: Path to the JSON file containing chapter data.
    The JSON file must be an array of chapter objects with configurable field names.

    Example JSON structure:
    [
        {
            "title": "Chapter 1: The Beginning",
            "content": "Chapter content here...",
            "metadata": { ... }
        },
        {
            "title": "Chapter 2: The Journey",
            "content": "More chapter content...",
            "metadata": { ... }
        }
    ]

    AI Enhancement:
    When --ai-enhance is used with --format md, the tool will use Google Gemini AI
    to improve Vietnamese writing style and quality. Requires GOOGLE_AI_API_KEY
    environment variable to be set.

    Field Mapping:
    Use --title-field and --content-field to specify custom JSON field names
    for chapter titles and content respectively.
    """
    # Set up logging
    if verbose:
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    else:
        logging.basicConfig(level=logging.WARNING)

    file_extension = f".{format}"

    # Validate AI enhancement options
    if ai_enhance and format != 'md':
        print("⚠️  AI enhancement is only available for Markdown format (--format md)")
        print("   Proceeding without AI enhancement...")
        ai_enhance = False

    # Validate field names
    if not title_field.strip():
        print("❌ Title field name cannot be empty")
        sys.exit(1)

    if not content_field.strip():
        print("❌ Content field name cannot be empty")
        sys.exit(1)

    converter = JsonToFilesConverter(
        output_dir=output_dir,
        file_extension=file_extension,
        enable_ai=ai_enhance,
        title_field=title_field,
        content_field=content_field
    )

    try:
        converter.convert(json_file)
    except KeyboardInterrupt:
        print("\nConversion interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()