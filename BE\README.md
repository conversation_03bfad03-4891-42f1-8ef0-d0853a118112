# Web Novel Backend Service

This is the backend service for the Web Novel application, built with Python and FastAPI. It provides a comprehensive API for scraping, managing, and enhancing web novel content.

## Features

- **FastAPI**: A modern, high-performance web framework for building APIs.
- **MongoDB**: A flexible, NoSQL database for storing story and chapter data.
- **Playwright**: A powerful library for reliable web scraping.
- **Google Gemini**: Integrated for AI-powered text enhancement.
- **Docker Support**: Fully containerized for easy setup and deployment.

---

## Project Structure

```
BE/
├── API/                # Main FastAPI application
│   ├── core/           # Application core (app creation, db connection)
│   ├── models/         # Pydantic models for data representation
│   ├── routers/        # API endpoint definitions
│   ├── services/       # Business logic and service layer
│   ├── config.py       # Application configuration
│   ├── Dockerfile      # Docker build instructions for the API
│   └── main.py         # Application entry point
├── Scraper/            # Web scraping logic and utilities
├── AITextEnhancer/     # AI text enhancement module
├── scripts/            # Utility scripts for database operations
│   ├── check_enhanced_content.py    # Check enhanced content in database
│   └── update_chapter_enhanced_content.py  # Update chapter content
├── tests/              # Test files
│   ├── test_chapter_enhancement.py  # Chapter enhancement tests
│   ├── test_enhancer.py             # Enhancer functionality tests
│   └── test_safety_filter.py        # Safety filter tests
├── .gitignore          # Git ignore patterns
├── docker-compose.yml  # Docker Compose for multi-container setup
└── README.md           # Project documentation
```

---

## Getting Started

You can run the backend service either locally using a Python environment or with Docker.

### Prerequisites

- Python 3.11+
- Poetry (for local development)
- Docker & Docker Compose (for containerized development)
- Access to a MongoDB instance
- A Google AI API Key for text enhancement features

### 1. Local Development Setup

#### a. Environment Configuration

Create a `.env` file in the `BE/API` directory by copying the template file. This file will store your environment-specific configurations.

```bash
# Copy the template file
cp API/.env.template API/.env
```

**File: `BE/API/.env`**
```env
# Application Settings
ENVIRONMENT=development
DEBUG=True

# Database (update with your local MongoDB connection string)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=webtruyen_dev

# Security (adjust for your frontend's URL)
ALLOWED_ORIGINS=http://localhost:3000

# AI Enhancement (add your key)
GOOGLE_AI_API_KEY="YOUR_GOOGLE_AI_API_KEY"
```

#### b. Install Dependencies

Navigate to the `BE` directory and install the required Python packages.

```bash
# Navigate to the API directory
cd BE/API

# Install dependencies
pip install -r requirements.txt

# Install Playwright browser dependencies
playwright install chromium
playwright install-deps chromium
```

#### c. Run the Application

From the `BE` directory, run the main application module.

```bash
# Install the watcher dependency
pip install watchfiles

# Run the development server with hot-reloading
python run_dev.py
```

The API will be accessible at `http://localhost:8000`.

> **Note for Windows Users:** The `run_dev.py` script is the recommended way to run the server during development. It correctly handles the `asyncio` event loop policy required by Playwright, avoiding a `NotImplementedError` that can occur when using `uvicorn`'s built-in reloader directly.

---

### 2. Docker Development Setup

Running the application with Docker is the recommended approach as it handles all dependencies and services automatically.

#### a. Environment Configuration

Create a `.env` file in the `BE/API` directory by copying the template file. This will be used by Docker Compose to inject environment variables into the containers.

```bash
# Copy the template file
cp API/.env.template API/.env
```

**File: `BE/API/.env`**
```env
# This key will be passed to the api service in docker-compose.yml
GOOGLE_AI_API_KEY="YOUR_GOOGLE_AI_API_KEY"
```

#### b. Build and Run Containers

From the `BE` directory, use Docker Compose to build and start the services.

```bash
# Build and start all services in detached mode
docker-compose up --build -d
```

This command will:
1.  Build the `webtruyen_api` Docker image.
2.  Start containers for the API, MongoDB, and Redis.
3.  Set up a shared network for the containers to communicate.

#### c. Accessing the Services

- **API**: `http://localhost:8000` (Docker) / `http://localhost:8000` (Local development)
- **API Docs (Swagger UI)**: `http://localhost:8000/docs` (Docker) / `http://localhost:8000/docs` (Local)
- **MongoDB**: Connect using `*******************************************`

#### d. Managing Docker Containers

- **View logs**: `docker-compose logs -f [service_name]` (e.g., `api`)
- **Stop services**: `docker-compose down`
- **Stop and remove volumes**: `docker-compose down -v` (useful for a clean restart)

---

## API Endpoints

Once the application is running, you can explore the available API endpoints via the interactive Swagger UI documentation:

- **Docker**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **Local Development**: [http://localhost:8000/docs](http://localhost:8000/docs)

Key endpoints include:
- `POST /scrape/story`: Scrape a single story from a URL.
- `GET /stories`: Retrieve a list of scraped stories.
- `GET /stories/{story_id}`: Get details for a specific story.

---

## Testing

The project includes comprehensive tests located in the `tests/` directory:

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test files
python tests/test_chapter_enhancement.py
python tests/test_enhancer.py
python tests/test_safety_filter.py
```

### Test Files

- **`test_chapter_enhancement.py`**: Tests the complete chapter enhancement workflow
- **`test_enhancer.py`**: Tests the AI text enhancer functionality
- **`test_safety_filter.py`**: Tests content safety filtering mechanisms

---

## Utility Scripts

The `scripts/` directory contains utility scripts for database operations:

### Available Scripts

```bash
# Check enhanced content in database
python scripts/check_enhanced_content.py

# Update chapter enhanced content
python scripts/update_chapter_enhanced_content.py
```

### Script Descriptions

- **`check_enhanced_content.py`**: Verifies and displays enhanced content status in the database
- **`update_chapter_enhanced_content.py`**: Updates chapter content with AI-enhanced versions

---

## Configuration

The project uses multiple configuration files:

- **`API/.env`**: Environment variables (API keys, database URLs) - Main configuration file
- **`API/.env.template`**: Template for environment variables
- **`Scraper/config.yaml`**: Scraper-specific settings (logging, browser options, target selectors)
- **`.gitignore`**: Comprehensive ignore patterns for Python projects

### Configuration Management

Ensure your `API/.env` file includes:
```env
MONGODB_URL=your_mongodb_connection_string
GOOGLE_AI_API_KEY=your_google_ai_api_key
```

**Note**: Use the `API/.env.template` file as a reference for all available configuration options.

---

## Development Guidelines

### Code Organization

- **API**: Main FastAPI application with modular structure
- **Scraper**: Web scraping functionality with Playwright
- **AITextEnhancer**: AI-powered text enhancement using Google Gemini
- **scripts/**: Database utility scripts
- **tests/**: Comprehensive test suite

### Best Practices

1. **Environment Management**: Always use `API/.env` file for sensitive configuration
2. **Testing**: Run tests before committing changes
3. **Code Style**: Follow PEP 8 guidelines for Python code
4. **Documentation**: Update README when adding new features
5. **Git**: Use the comprehensive `.gitignore` to avoid committing unnecessary files

### Common Commands

```bash
# Development server
python run_dev.py

# Run tests
python -m pytest tests/

# Check enhanced content
python scripts/check_enhanced_content.py

# Docker development
docker-compose up --build -d
```

---

## Troubleshooting

### Common Issues

1. **Playwright Installation**: Run `playwright install chromium` if browser dependencies are missing
2. **MongoDB Connection**: Ensure MongoDB is running and connection string is correct
3. **API Key Issues**: Verify Google AI API key is set in `API/.env` file
4. **Port Conflicts**: Check if ports 8000 (API) or 27017 (MongoDB) are already in use

### Logs and Debugging

- Application logs are stored in `logs/` directory
- Use `docker-compose logs -f api` for Docker container logs
- Enable debug mode in `API/.env` with `DEBUG=True`

---

## Contributing

1. Follow the established project structure
2. Add tests for new functionality
3. Update documentation as needed
4. Use meaningful commit messages
5. Test both local and Docker environments before submitting changes
