# Web Novel Backend Service

This is the backend service for the Web Novel application, built with Python and FastAPI. It provides a comprehensive API for scraping, managing, and enhancing web novel content.

## Features

- **FastAPI**: A modern, high-performance web framework for building APIs.
- **MongoDB**: A flexible, NoSQL database for storing story and chapter data.
- **Playwright**: A powerful library for reliable web scraping.
- **Google Gemini**: Integrated for AI-powered text enhancement.
- **Docker Support**: Fully containerized for easy setup and deployment.

---

## Project Structure

```
BE/
├── API/                # Main FastAPI application
│   ├── core/           # Application core (app creation, db connection)
│   ├── models/         # Pydantic models for data representation
│   ├── routers/        # API endpoint definitions
│   ├── services/       # Business logic and service layer
│   ├── config.py       # Application configuration
│   ├── Dockerfile      # Docker build instructions for the API
│   └── main.py         # Application entry point
├── Scraper/            # Web scraping logic and utilities
├── AITextEnhancer/     # AI text enhancement module
└── docker-compose.yml  # Docker Compose for multi-container setup
```

---

## Getting Started

You can run the backend service either locally using a Python environment or with Docker.

### Prerequisites

- Python 3.11+
- Poetry (for local development)
- Docker & Docker Compose (for containerized development)
- Access to a MongoDB instance
- A Google AI API Key for text enhancement features

### 1. Local Development Setup

#### a. Environment Configuration

Create a `.env` file in the `BE/API` directory. This file will store your environment-specific configurations. You can use the example below as a template.

**File: `BE/API/.env`**
```env
# Application Settings
ENVIRONMENT=development
DEBUG=True

# Database (update with your local MongoDB connection string)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=webtruyen_dev

# Security (adjust for your frontend's URL)
ALLOWED_ORIGINS=http://localhost:3000

# AI Enhancement (add your key)
GOOGLE_AI_API_KEY="YOUR_GOOGLE_AI_API_KEY"
```

#### b. Install Dependencies

Navigate to the `BE` directory and install the required Python packages.

```bash
# Navigate to the API directory
cd BE/API

# Install dependencies
pip install -r requirements.txt

# Install Playwright browser dependencies
playwright install chromium
playwright install-deps chromium
```

#### c. Run the Application

From the `BE` directory, run the main application module.

```bash
# Install the watcher dependency
pip install watchfiles

# Run the development server with hot-reloading
python run_dev.py
```

The API will be accessible at `http://localhost:8000`.

> **Note for Windows Users:** The `run_dev.py` script is the recommended way to run the server during development. It correctly handles the `asyncio` event loop policy required by Playwright, avoiding a `NotImplementedError` that can occur when using `uvicorn`'s built-in reloader directly.

---

### 2. Docker Development Setup

Running the application with Docker is the recommended approach as it handles all dependencies and services automatically.

#### a. Environment Configuration

Create a `.env` file in the `BE` directory (alongside `docker-compose.yml`). This will be used by Docker Compose to inject environment variables into the containers.

**File: `BE/.env`**
```env
# This key will be passed to the api service in docker-compose.yml
GOOGLE_AI_API_KEY="YOUR_GOOGLE_AI_API_KEY"
```

#### b. Build and Run Containers

From the `BE` directory, use Docker Compose to build and start the services.

```bash
# Build and start all services in detached mode
docker-compose up --build -d
```

This command will:
1.  Build the `webtruyen_api` Docker image.
2.  Start containers for the API, MongoDB, and Redis.
3.  Set up a shared network for the containers to communicate.

#### c. Accessing the Services

- **API**: `http://localhost:8000` (Docker) / `http://localhost:8000` (Local development)
- **API Docs (Swagger UI)**: `http://localhost:8000/docs` (Docker) / `http://localhost:8000/docs` (Local)
- **MongoDB**: Connect using `*******************************************`

#### d. Managing Docker Containers

- **View logs**: `docker-compose logs -f [service_name]` (e.g., `api`)
- **Stop services**: `docker-compose down`
- **Stop and remove volumes**: `docker-compose down -v` (useful for a clean restart)

---

## API Endpoints

Once the application is running, you can explore the available API endpoints via the interactive Swagger UI documentation:

- **Docker**: [http://localhost:8000/docs](http://localhost:8000/docs)
- **Local Development**: [http://localhost:8000/docs](http://localhost:8000/docs)

Key endpoints include:
- `POST /scrape/story`: Scrape a single story from a URL.
- `GET /stories`: Retrieve a list of scraped stories.
- `GET /stories/{story_id}`: Get details for a specific story.
