#!/bin/bash

# MongoDB Quick Operations Script for Webtruyen Container
# =====================================================
# 
# This script provides quick MongoDB operations for your container
# without needing to enter the Python interactive tool.
#
# Usage:
#   ./mongo_quick.sh [command] [options]
#
# Commands:
#   status     - Check container status
#   start      - Start MongoDB container
#   stop       - Stop MongoDB container
#   restart    - Restart MongoDB container
#   logs       - Show container logs
#   shell      - Open MongoDB shell
#   backup     - Backup database
#   restore    - Restore database
#   collections - List all collections
#   help       - Show this help

# Configuration
CONTAINER_NAME="webtruyen_mongodb"
DB_NAME="webtruyen_api"
DB_USER="admin"
DB_PASS="password123"
DB_PORT="27017"
BACKUP_DIR="./backups"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi
}

# Check container status
check_container_status() {
    log_info "Checking container status..."
    
    if docker ps -f "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}" | grep -q "$CONTAINER_NAME"; then
        log_success "Container $CONTAINER_NAME is running"
        docker ps -f "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        return 0
    else
        log_warning "Container $CONTAINER_NAME is not running"
        return 1
    fi
}

# Start container
start_container() {
    log_info "Starting container $CONTAINER_NAME..."
    
    if docker start "$CONTAINER_NAME" &> /dev/null; then
        log_success "Container started successfully"
        sleep 3
        check_container_status
    else
        log_error "Failed to start container"
        exit 1
    fi
}

# Stop container
stop_container() {
    log_info "Stopping container $CONTAINER_NAME..."
    
    if docker stop "$CONTAINER_NAME" &> /dev/null; then
        log_success "Container stopped successfully"
    else
        log_error "Failed to stop container"
        exit 1
    fi
}

# Restart container
restart_container() {
    log_info "Restarting container $CONTAINER_NAME..."
    
    if docker restart "$CONTAINER_NAME" &> /dev/null; then
        log_success "Container restarted successfully"
        sleep 3
        check_container_status
    else
        log_error "Failed to restart container"
        exit 1
    fi
}

# Show container logs
show_logs() {
    local lines=${1:-50}
    log_info "Showing last $lines lines of container logs..."
    
    docker logs --tail "$lines" "$CONTAINER_NAME"
}

# Open MongoDB shell
open_shell() {
    log_info "Opening MongoDB shell..."
    
    if ! check_container_status > /dev/null 2>&1; then
        log_error "Container is not running. Starting container first..."
        start_container
    fi
    
    log_info "Connecting to MongoDB shell..."
    docker exec -it "$CONTAINER_NAME" mongosh mongodb://"$DB_USER":"$DB_PASS"@localhost:27017/"$DB_NAME" --authenticationDatabase admin
}

# List collections
list_collections() {
    log_info "Listing collections in database $DB_NAME..."
    
    if ! check_container_status > /dev/null 2>&1; then
        log_error "Container is not running. Please start it first."
        exit 1
    fi
    
    docker exec "$CONTAINER_NAME" mongosh --host localhost --port 27017 \
        --username "$DB_USER" --password "$DB_PASS" --authenticationDatabase admin \
        --eval "db.runCommand('listCollections').cursor.firstBatch.forEach(printjson)" "$DB_NAME"
}

# Backup database
backup_database() {
    local backup_name="${1:-backup_$(date +%Y%m%d_%H%M%S)}"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    log_info "Creating backup: $backup_name"
    
    if ! check_container_status > /dev/null 2>&1; then
        log_error "Container is not running. Please start it first."
        exit 1
    fi
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Create backup using mongodump
    docker exec "$CONTAINER_NAME" mongodump --host localhost --port 27017 \
        --username "$DB_USER" --password "$DB_PASS" --authenticationDatabase admin \
        --db "$DB_NAME" --out "/tmp/backup"
    
    # Copy backup from container to host
    docker cp "$CONTAINER_NAME:/tmp/backup/$DB_NAME" "$backup_path"
    
    # Clean up temporary backup in container
    docker exec "$CONTAINER_NAME" rm -rf "/tmp/backup"
    
    if [ -d "$backup_path" ]; then
        log_success "Backup created successfully: $backup_path"
    else
        log_error "Backup failed"
        exit 1
    fi
}

# Restore database
restore_database() {
    local backup_path="$1"
    
    if [ -z "$backup_path" ]; then
        log_error "Please provide backup path"
        log_info "Usage: $0 restore <backup_path>"
        exit 1
    fi
    
    if [ ! -d "$backup_path" ]; then
        log_error "Backup path does not exist: $backup_path"
        exit 1
    fi
    
    log_warning "This will replace the current database. Are you sure? (y/N)"
    read -r confirm
    
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "Restore cancelled"
        exit 0
    fi
    
    log_info "Restoring database from: $backup_path"
    
    if ! check_container_status > /dev/null 2>&1; then
        log_error "Container is not running. Please start it first."
        exit 1
    fi
    
    # Copy backup to container
    docker cp "$backup_path" "$CONTAINER_NAME:/tmp/restore"
    
    # Restore using mongorestore
    docker exec "$CONTAINER_NAME" mongorestore --host localhost --port 27017 \
        --username "$DB_USER" --password "$DB_PASS" --authenticationDatabase admin \
        --db "$DB_NAME" --drop "/tmp/restore"
    
    # Clean up temporary restore in container
    docker exec "$CONTAINER_NAME" rm -rf "/tmp/restore"
    
    log_success "Database restored successfully"
}

# Quick database stats
db_stats() {
    log_info "Getting database statistics..."
    
    if ! check_container_status > /dev/null 2>&1; then
        log_error "Container is not running. Please start it first."
        exit 1
    fi
    
    docker exec "$CONTAINER_NAME" mongosh --host localhost --port 27017 \
        --username "$DB_USER" --password "$DB_PASS" --authenticationDatabase admin \
        --eval "
            print('📊 Database Statistics:');
            print('Database:', db.getName());
            print('Collections:', db.runCommand('listCollections').cursor.firstBatch.length);
            print('');
            print('📋 Collection Details:');
            db.runCommand('listCollections').cursor.firstBatch.forEach(function(collection) {
                var stats = db.runCommand({collStats: collection.name});
                print('  📁', collection.name);
                print('     Documents:', stats.count || 0);
                print('     Size:', (stats.size || 0), 'bytes');
                print('     Indexes:', stats.nindexes || 0);
                print('');
            });
        " "$DB_NAME"
}

# Show help
show_help() {
    echo "MongoDB Quick Operations Script for Webtruyen Container"
    echo "====================================================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  status              - Check container status"
    echo "  start               - Start MongoDB container"
    echo "  stop                - Stop MongoDB container"
    echo "  restart             - Restart MongoDB container"
    echo "  logs [lines]        - Show container logs (default: 50 lines)"
    echo "  shell               - Open MongoDB shell"
    echo "  collections         - List all collections"
    echo "  stats               - Show database statistics"
    echo "  backup [name]       - Backup database"
    echo "  restore <path>      - Restore database from backup"
    echo "  help                - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 status           - Check if container is running"
    echo "  $0 start            - Start the container"
    echo "  $0 logs 100         - Show last 100 log lines"
    echo "  $0 backup my_backup - Create backup named 'my_backup'"
    echo "  $0 restore ./backups/my_backup - Restore from backup"
    echo ""
    echo "Configuration:"
    echo "  Container: $CONTAINER_NAME"
    echo "  Database:  $DB_NAME"
    echo "  Port:      $DB_PORT"
    echo "  Backup Dir: $BACKUP_DIR"
}

# Main script logic
main() {
    check_docker
    
    case "$1" in
        "status")
            check_container_status
            ;;
        "start")
            start_container
            ;;
        "stop")
            stop_container
            ;;
        "restart")
            restart_container
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            open_shell
            ;;
        "collections")
            list_collections
            ;;
        "stats")
            db_stats
            ;;
        "backup")
            backup_database "$2"
            ;;
        "restore")
            restore_database "$2"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            log_info "Use '$0 help' to see available commands"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

