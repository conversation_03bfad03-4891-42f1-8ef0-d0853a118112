// MongoDB Initialization Script for Vietnamese Web API

// Switch to the application database
db = db.getSiblingDB('webtruyen_api');

// Create application user
db.createUser({
  user: 'api_user',
  pwd: 'api_password',
  roles: [
    {
      role: 'readWrite',
      db: 'webtruyen_api'
    }
  ]
});

// Create collections with validation
db.createCollection('stories', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'url', 'created_at', 'updated_at'],
      properties: {
        title: {
          bsonType: 'string',
          description: 'Story title is required and must be a string'
        },
        url: {
          bsonType: 'string',
          description: 'Story URL is required and must be a string'
        },
        slug: {
          bsonType: 'string',
          description: 'URL-friendly slug'
        },
        total_chapters_scraped: {
          bsonType: 'int',
          minimum: 0,
          description: 'Number of scraped chapters'
        },
        total_chapters_enhanced: {
          bsonType: 'int',
          minimum: 0,
          description: 'Number of enhanced chapters'
        },
        enhancement_progress: {
          bsonType: 'double',
          minimum: 0,
          maximum: 100,
          description: 'Enhancement progress percentage'
        },
        scraping_status: {
          bsonType: 'string',
          enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled'],
          description: 'Scraping job status'
        },
        metadata: {
          bsonType: 'object',
          properties: {
            author: { bsonType: 'string' },
            description: { bsonType: 'string' },
            genres: { bsonType: 'array' },
            tags: { bsonType: 'array' },
            status: {
              bsonType: 'string',
              enum: ['ongoing', 'completed', 'hiatus', 'dropped', 'unknown']
            },
            total_chapters: { bsonType: 'int', minimum: 0 },
            cover_image_url: { bsonType: 'string' },
            source_website: { bsonType: 'string' }
          }
        },
        created_at: {
          bsonType: 'date',
          description: 'Creation timestamp'
        },
        updated_at: {
          bsonType: 'date',
          description: 'Last update timestamp'
        }
      }
    }
  }
});

db.createCollection('chapters', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['story_id', 'chapter_number', 'title', 'url', 'created_at', 'updated_at'],
      properties: {
        story_id: {
          bsonType: 'objectId',
          description: 'Reference to parent story'
        },
        chapter_number: {
          bsonType: 'int',
          minimum: 1,
          description: 'Chapter number within the story'
        },
        title: {
          bsonType: 'string',
          description: 'Chapter title'
        },
        url: {
          bsonType: 'string',
          description: 'Chapter URL'
        },
        original_content: {
          bsonType: 'string',
          description: 'Original scraped content'
        },
        enhanced_content: {
          bsonType: 'string',
          description: 'AI-enhanced content'
        },
        is_scraped: {
          bsonType: 'bool',
          description: 'Whether chapter has been scraped'
        },
        is_enhanced: {
          bsonType: 'bool',
          description: 'Whether chapter has been enhanced'
        },
        enhancement_status: {
          bsonType: 'string',
          enum: ['not_enhanced', 'pending', 'in_progress', 'enhanced', 'failed'],
          description: 'Enhancement status'
        }
      }
    }
  }
});

db.createCollection('scraping_jobs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['job_type', 'status', 'created_at'],
      properties: {
        story_id: { bsonType: 'objectId' },
        job_type: {
          bsonType: 'string',
          enum: ['story_info', 'chapters', 'single_chapter', 'batch_chapters'],
          description: 'Type of scraping job'
        },
        status: {
          bsonType: 'string',
          enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled'],
          description: 'Job status'
        },
        total_items: { bsonType: 'int', minimum: 0 },
        completed_items: { bsonType: 'int', minimum: 0 },
        failed_items: { bsonType: 'int', minimum: 0 },
        progress_percentage: { bsonType: 'double', minimum: 0, maximum: 100 },
        chapter_urls: { bsonType: 'array' },
        max_concurrent: { bsonType: 'int', minimum: 1 },
        rate_limit_delay: { bsonType: 'double', minimum: 0 },
        errors: { bsonType: 'array' },
        results: { bsonType: 'object' }
      }
    }
  }
});

db.createCollection('enhancement_jobs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['total_chapters', 'status', 'created_at'],
      properties: {
        story_id: { bsonType: 'objectId' },
        chapter_ids: { bsonType: 'array' },
        status: {
          bsonType: 'string',
          enum: ['pending', 'in_progress', 'completed', 'failed', 'cancelled'],
          description: 'Job status'
        },
        total_chapters: { bsonType: 'int', minimum: 0 },
        completed_chapters: { bsonType: 'int', minimum: 0 },
        failed_chapters: { bsonType: 'int', minimum: 0 },
        progress_percentage: { bsonType: 'double', minimum: 0, maximum: 100 },
        model_name: { bsonType: 'string' },
        batch_size: { bsonType: 'int', minimum: 1 },
        errors: { bsonType: 'array' },
        results: { bsonType: 'object' }
      }
    }
  }
});

// Create indexes for optimal performance
print('Creating indexes...');

// Stories indexes
db.stories.createIndex({ 'url': 1 }, { unique: true });
db.stories.createIndex({ 'slug': 1 });
db.stories.createIndex({ 'title': 1 });
db.stories.createIndex({ 'scraping_status': 1 });
db.stories.createIndex({ 'created_at': -1 });
db.stories.createIndex({ 'metadata.author': 1 });
db.stories.createIndex({ 'metadata.status': 1 });
db.stories.createIndex({ 'metadata.genres': 1 });

// Chapters indexes
db.chapters.createIndex({ 'story_id': 1, 'chapter_number': 1 }, { unique: true });
db.chapters.createIndex({ 'url': 1 }, { unique: true });
db.chapters.createIndex({ 'story_id': 1 });
db.chapters.createIndex({ 'is_scraped': 1 });
db.chapters.createIndex({ 'is_enhanced': 1 });
db.chapters.createIndex({ 'enhancement_status': 1 });
db.chapters.createIndex({ 'created_at': -1 });

// Job indexes
db.scraping_jobs.createIndex({ 'story_id': 1 });
db.scraping_jobs.createIndex({ 'status': 1 });
db.scraping_jobs.createIndex({ 'created_at': -1 });
db.scraping_jobs.createIndex({ 'job_type': 1 });

db.enhancement_jobs.createIndex({ 'story_id': 1 });
db.enhancement_jobs.createIndex({ 'status': 1 });
db.enhancement_jobs.createIndex({ 'created_at': -1 });

// Text search indexes
db.stories.createIndex({
  'title': 'text',
  'metadata.author': 'text',
  'metadata.description': 'text'
}, {
  name: 'story_text_search',
  default_language: 'none'  // Disable language-specific stemming for Vietnamese
});

print('Database initialization completed successfully!');
print('Created collections: stories, chapters, scraping_jobs, enhancement_jobs');
print('Created user: api_user');
print('Created indexes for optimal performance');
print('Text search enabled for stories');

// Insert sample data (optional)
print('Inserting sample data...');

// Sample story
var sampleStoryId = ObjectId();
db.stories.insertOne({
  _id: sampleStoryId,
  title: 'Sample Vietnamese Novel',
  url: 'https://webtruyen.diendantruyen.com/sample-story/',
  slug: 'sample-vietnamese-novel',
  total_chapters_scraped: 0,
  total_chapters_enhanced: 0,
  enhancement_progress: 0.0,
  scraping_status: 'pending',
  metadata: {
    author: 'Sample Author',
    description: 'This is a sample Vietnamese web novel for testing purposes.',
    genres: ['Romance', 'Drama'],
    tags: ['Modern', 'School Life'],
    status: 'ongoing',
    total_chapters: 0,
    source_website: 'webtruyen'
  },
  created_at: new Date(),
  updated_at: new Date()
});

print('Sample data inserted successfully!');
print('Ready to use Vietnamese Web API!');
