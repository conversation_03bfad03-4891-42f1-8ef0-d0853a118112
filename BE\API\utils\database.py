"""
Database Utilities and Connection Management

This module provides utilities for MongoDB operations, connection management,
and common database patterns used throughout the API.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Union
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorCollection, AsyncIOMotorDatabase
from pymongo import ASCENDING, DESCENDING
from pymongo.errors import Duplicate<PERSON>eyError, PyMongoError

from API.models.database import db_manager, Story, Chapter, ScrapingJob, EnhancementJob
from API.middleware.error_handling import DatabaseError
from API.utils.logging_config import LoggerMixin


class DatabaseService(LoggerMixin):
    """Base database service with common operations"""
    
    def __init__(self, collection_name: str):
        self.collection_name = collection_name
    
    @property
    def collection(self) -> AsyncIOMotorCollection:
        """Get the MongoDB collection"""
        if db_manager.database is None:
            raise DatabaseError("Database not connected")
        return db_manager.database[self.collection_name]
    
    async def find_by_id(self, doc_id: Union[str, ObjectId]) -> Optional[Dict[str, Any]]:
        """Find document by ID"""
        try:
            if isinstance(doc_id, str):
                doc_id = ObjectId(doc_id)
            
            document = await self.collection.find_one({"_id": doc_id})
            return document
            
        except Exception as e:
            self.log_error(f"Error finding document by ID {doc_id}: {e}")
            raise DatabaseError(f"Failed to find document: {e}")
    
    async def find_one(self, filter_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find single document by filter"""
        try:
            document = await self.collection.find_one(filter_dict)
            return document
            
        except Exception as e:
            self.log_error(f"Error finding document with filter {filter_dict}: {e}")
            raise DatabaseError(f"Failed to find document: {e}")
    
    async def find_many(
        self,
        filter_dict: Dict[str, Any] = None,
        sort: List[tuple] = None,
        limit: int = None,
        skip: int = None
    ) -> List[Dict[str, Any]]:
        """Find multiple documents"""
        try:
            cursor = self.collection.find(filter_dict or {})
            
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            
            documents = await cursor.to_list(length=limit)
            return documents
            
        except Exception as e:
            self.log_error(f"Error finding documents: {e}")
            raise DatabaseError(f"Failed to find documents: {e}")
    
    async def count_documents(self, filter_dict: Dict[str, Any] = None) -> int:
        """Count documents matching filter"""
        try:
            count = await self.collection.count_documents(filter_dict or {})
            return count
            
        except Exception as e:
            self.log_error(f"Error counting documents: {e}")
            raise DatabaseError(f"Failed to count documents: {e}")
    
    async def insert_one(self, document: Dict[str, Any]) -> ObjectId:
        """Insert single document"""
        try:
            # Add timestamps
            now = datetime.utcnow()
            document.setdefault('created_at', now)
            document.setdefault('updated_at', now)
            
            result = await self.collection.insert_one(document)
            self.log_info(f"Inserted document with ID: {result.inserted_id}")
            return result.inserted_id
            
        except DuplicateKeyError as e:
            self.log_warning(f"Duplicate key error: {e}")
            raise DatabaseError(f"Document already exists: {e}")
        except Exception as e:
            self.log_error(f"Error inserting document: {e}")
            raise DatabaseError(f"Failed to insert document: {e}")
    
    async def insert_many(self, documents: List[Dict[str, Any]]) -> List[ObjectId]:
        """Insert multiple documents"""
        try:
            # Add timestamps to all documents
            now = datetime.utcnow()
            for doc in documents:
                doc.setdefault('created_at', now)
                doc.setdefault('updated_at', now)
            
            result = await self.collection.insert_many(documents)
            self.log_info(f"Inserted {len(result.inserted_ids)} documents")
            return result.inserted_ids
            
        except Exception as e:
            self.log_error(f"Error inserting documents: {e}")
            raise DatabaseError(f"Failed to insert documents: {e}")
    
    async def update_one(
        self,
        filter_dict: Dict[str, Any],
        update_dict: Dict[str, Any],
        upsert: bool = False
    ) -> bool:
        """Update single document"""
        try:
            # Add updated timestamp
            update_dict.setdefault('$set', {})['updated_at'] = datetime.utcnow()
            
            result = await self.collection.update_one(filter_dict, update_dict, upsert=upsert)
            
            if result.modified_count > 0:
                self.log_info(f"Updated document matching filter: {filter_dict}")
                return True
            elif result.upserted_id:
                self.log_info(f"Upserted document with ID: {result.upserted_id}")
                return True
            else:
                self.log_warning(f"No document updated for filter: {filter_dict}")
                return False
                
        except Exception as e:
            self.log_error(f"Error updating document: {e}")
            raise DatabaseError(f"Failed to update document: {e}")
    
    async def update_by_id(
        self,
        doc_id: Union[str, ObjectId],
        update_dict: Dict[str, Any]
    ) -> bool:
        """Update document by ID"""
        if isinstance(doc_id, str):
            doc_id = ObjectId(doc_id)
        
        return await self.update_one({"_id": doc_id}, update_dict)
    
    async def delete_one(self, filter_dict: Dict[str, Any]) -> bool:
        """Delete single document"""
        try:
            result = await self.collection.delete_one(filter_dict)
            
            if result.deleted_count > 0:
                self.log_info(f"Deleted document matching filter: {filter_dict}")
                return True
            else:
                self.log_warning(f"No document deleted for filter: {filter_dict}")
                return False
                
        except Exception as e:
            self.log_error(f"Error deleting document: {e}")
            raise DatabaseError(f"Failed to delete document: {e}")
    
    async def delete_by_id(self, doc_id: Union[str, ObjectId]) -> bool:
        """Delete document by ID"""
        if isinstance(doc_id, str):
            doc_id = ObjectId(doc_id)
        
        return await self.delete_one({"_id": doc_id})
    
    async def aggregate(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute aggregation pipeline"""
        try:
            cursor = self.collection.aggregate(pipeline)
            results = await cursor.to_list(length=None)
            return results
            
        except Exception as e:
            self.log_error(f"Error executing aggregation: {e}")
            raise DatabaseError(f"Failed to execute aggregation: {e}")


class StoryService(DatabaseService):
    """Service for story-related database operations"""
    
    def __init__(self):
        super().__init__("stories")
    
    async def find_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Find story by URL"""
        return await self.find_one({"url": url})
    
    async def find_by_slug(self, slug: str) -> Optional[Dict[str, Any]]:
        """Find story by slug"""
        return await self.find_one({"slug": slug})
    
    async def search_stories(
        self,
        query: str = None,
        author: str = None,
        status: str = None,
        genre: str = None,
        scraping_status: str = None,
        page: int = 1,
        page_size: int = 20,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> tuple[List[Dict[str, Any]], int]:
        """Search stories with filters and pagination"""
        
        # Build filter
        filter_dict = {}
        
        if query:
            filter_dict["$or"] = [
                {"title": {"$regex": query, "$options": "i"}},
                {"metadata.description": {"$regex": query, "$options": "i"}}
            ]
        
        if author:
            filter_dict["metadata.author"] = {"$regex": author, "$options": "i"}
        
        if status:
            filter_dict["metadata.status"] = status
        
        if genre:
            filter_dict["metadata.genres"] = genre
        
        if scraping_status:
            filter_dict["scraping_status"] = scraping_status
        
        # Get total count
        total_count = await self.count_documents(filter_dict)
        
        # Build sort
        sort_direction = DESCENDING if sort_order == "desc" else ASCENDING
        sort = [(sort_by, sort_direction)]
        
        # Calculate pagination
        skip = (page - 1) * page_size
        
        # Get documents
        documents = await self.find_many(
            filter_dict=filter_dict,
            sort=sort,
            limit=page_size,
            skip=skip
        )
        
        return documents, total_count
    
    async def update_scraping_progress(
        self,
        story_id: Union[str, ObjectId],
        scraped_chapters: int,
        total_chapters: int = None
    ):
        """Update story scraping progress"""
        update_dict = {
            "$set": {
                "total_chapters_scraped": scraped_chapters,
                "last_scraped_at": datetime.utcnow()
            }
        }
        
        if total_chapters is not None:
            update_dict["$set"]["metadata.total_chapters"] = total_chapters
        
        return await self.update_by_id(story_id, update_dict)
    
    async def update_enhancement_progress(
        self,
        story_id: Union[str, ObjectId],
        enhanced_chapters: int
    ):
        """Update story enhancement progress"""
        # Get current story to calculate percentage
        story = await self.find_by_id(story_id)
        if not story:
            return False
        
        total_chapters = story.get("total_chapters_scraped", 0)
        progress = (enhanced_chapters / total_chapters * 100) if total_chapters > 0 else 0
        
        update_dict = {
            "$set": {
                "total_chapters_enhanced": enhanced_chapters,
                "enhancement_progress": progress
            }
        }
        
        return await self.update_by_id(story_id, update_dict)


class ChapterService(DatabaseService):
    """Service for chapter-related database operations"""
    
    def __init__(self):
        super().__init__("chapters")
    
    async def find_by_story_and_number(
        self,
        story_id: Union[str, ObjectId],
        chapter_number: int
    ) -> Optional[Dict[str, Any]]:
        """Find chapter by story ID and chapter number"""
        if isinstance(story_id, str):
            story_id = ObjectId(story_id)
        
        return await self.find_one({
            "story_id": story_id,
            "chapter_number": chapter_number
        })
    
    async def find_by_story(
        self,
        story_id: Union[str, ObjectId],
        enhanced_only: bool = False,
        scraped_only: bool = False,
        page: int = 1,
        page_size: int = 50
    ) -> tuple[List[Dict[str, Any]], int]:
        """Find chapters by story with pagination"""
        if isinstance(story_id, str):
            story_id = ObjectId(story_id)
        
        # Build filter
        filter_dict = {"story_id": story_id}
        
        if enhanced_only:
            filter_dict["is_enhanced"] = True
        
        if scraped_only:
            filter_dict["is_scraped"] = True
        
        # Get total count
        total_count = await self.count_documents(filter_dict)
        
        # Calculate pagination
        skip = (page - 1) * page_size
        
        # Get documents sorted by chapter number
        documents = await self.find_many(
            filter_dict=filter_dict,
            sort=[("chapter_number", ASCENDING)],
            limit=page_size,
            skip=skip
        )
        
        return documents, total_count
    
    async def find_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Find chapter by URL"""
        return await self.find_one({"url": url})
    
    async def get_enhancement_candidates(
        self,
        story_id: Union[str, ObjectId] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get chapters that need enhancement"""
        filter_dict = {
            "is_scraped": True,
            "is_enhanced": False,
            "original_content": {"$ne": None, "$ne": ""}
        }
        
        if story_id:
            if isinstance(story_id, str):
                story_id = ObjectId(story_id)
            filter_dict["story_id"] = story_id
        
        return await self.find_many(
            filter_dict=filter_dict,
            sort=[("created_at", ASCENDING)],
            limit=limit
        )


class JobService(DatabaseService):
    """Service for job-related database operations"""
    
    def __init__(self, collection_name: str):
        super().__init__(collection_name)
    
    async def create_job(self, job_data: Dict[str, Any]) -> ObjectId:
        """Create a new job"""
        job_data.setdefault("status", "pending")
        job_data.setdefault("progress_percentage", 0.0)
        job_data.setdefault("completed_items", 0)
        job_data.setdefault("failed_items", 0)
        job_data.setdefault("errors", [])
        job_data.setdefault("results", {})
        
        return await self.insert_one(job_data)
    
    async def update_job_progress(
        self,
        job_id: Union[str, ObjectId],
        completed_items: int,
        failed_items: int = None,
        status: str = None,
        errors: List[str] = None,
        results: Dict[str, Any] = None
    ):
        """Update job progress"""
        job = await self.find_by_id(job_id)
        if not job:
            return False
        
        total_items = job.get("total_items", 0)
        progress = (completed_items / total_items * 100) if total_items > 0 else 0
        
        update_dict = {
            "$set": {
                "completed_items": completed_items,
                "progress_percentage": progress
            }
        }
        
        if failed_items is not None:
            update_dict["$set"]["failed_items"] = failed_items
        
        if status:
            update_dict["$set"]["status"] = status
            if status == "in_progress" and not job.get("started_at"):
                update_dict["$set"]["started_at"] = datetime.utcnow()
            elif status in ["completed", "failed", "cancelled"]:
                update_dict["$set"]["completed_at"] = datetime.utcnow()
        
        if errors:
            update_dict["$push"] = {"errors": {"$each": errors}}
        
        if results:
            update_dict["$set"]["results"] = results
        
        return await self.update_by_id(job_id, update_dict)
    
    async def cleanup_old_jobs(self, retention_days: int = 7):
        """Clean up old completed jobs"""
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        filter_dict = {
            "status": {"$in": ["completed", "failed", "cancelled"]},
            "completed_at": {"$lt": cutoff_date}
        }
        
        result = await self.collection.delete_many(filter_dict)
        self.log_info(f"Cleaned up {result.deleted_count} old jobs")
        return result.deleted_count


# ============================================================================
# Service Instances
# ============================================================================

story_service = StoryService()
chapter_service = ChapterService()
scraping_job_service = JobService("scraping_jobs")
enhancement_job_service = JobService("enhancement_jobs")
