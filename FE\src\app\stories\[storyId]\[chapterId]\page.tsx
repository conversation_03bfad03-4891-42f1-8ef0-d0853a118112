import { fetchStoryById, fetchChapterByNumber } from '@/services/storyService';
import ChapterReaderWrapper from '@/components/reader/ChapterReaderWrapper';
import { notFound } from 'next/navigation';

interface ReaderPageProps {
  params: {
    storyId: string;
    chapterId: string;
  };
}

export default async function ReaderPage({ params }: ReaderPageProps) {
  try {
    const chapterNumber = parseInt(params.chapterId);
    // Only fetch essential data server-side
    const [story, chapter] = await Promise.all([
      fetchStoryById(params.storyId),
      fetchChapterByNumber(params.storyId, chapterNumber)
    ]);

    console.log("chapter", chapter);

    // Navigation will be handled client-side
    const previousChapter = null;
    const nextChapter = null;

    return (
      <ChapterReaderWrapper
        story={story}
        chapter={chapter}
        storyId={params.storyId}
      />
    );
  } catch (error) {
    console.error('Error fetching chapter:', error);
    notFound();
  }
}

export async function generateMetadata({ params }: ReaderPageProps) {
  try {
    const chapterNumber = parseInt(params.chapterId);
    const [story, chapter] = await Promise.all([
      fetchStoryById(params.storyId),
      fetchChapterByNumber(params.storyId, chapterNumber)
    ]);

    const title = chapter.title
      ? `${story.title} - Chapter ${chapter.chapter_number}: ${chapter.title}`
      : `${story.title} - Chapter ${chapter.chapter_number}`;

    return {
      title: `${title} - WebTruyen`,
      description: `Read ${title} by author ${story.author}`,
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Chapter Not Found - WebTruyen',
    };
  }
}