import { useState, useEffect } from 'react';
import { Story } from '@/types/story';

interface StoryStats {
  total_chapters: number;
  total_chapters_scraped: number;
  total_chapters_enhanced: number;
  loading: boolean;
  error: string | null;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

/**
 * Hook to fetch and manage story statistics
 * This ensures we get the most up-to-date chapter counts
 */
export const useStoryStats = (storyId: string, initialStory?: Story): StoryStats => {
  const [stats, setStats] = useState<StoryStats>({
    total_chapters: initialStory?.total_chapters || 0,
    total_chapters_scraped: initialStory?.total_chapters_scraped || 0,
    total_chapters_enhanced: initialStory?.total_chapters_enhanced || 0,
    loading: false,
    error: null
  });

  const fetchStoryStats = async () => {
    if (!storyId) return;

    setStats(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Fetch fresh story data to get updated counts
      const response = await fetch(`${API_BASE_URL}/api/v1/stories/${storyId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch story stats: ${response.status}`);
      }

      const storyData = await response.json();
      
      setStats({
        total_chapters: storyData.total_chapters || 0,
        total_chapters_scraped: storyData.total_chapters_scraped || 0,
        total_chapters_enhanced: storyData.total_chapters_enhanced || 0,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('Error fetching story stats:', error);
      setStats(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch story stats'
      }));
    }
  };

  useEffect(() => {
    fetchStoryStats();
  }, [storyId]);

  // Refresh stats function that can be called externally
  const refreshStats = () => {
    fetchStoryStats();
  };

  return {
    ...stats,
    refreshStats
  } as StoryStats & { refreshStats: () => void };
};