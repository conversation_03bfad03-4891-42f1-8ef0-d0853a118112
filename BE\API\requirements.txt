# FastAPI and ASGI server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Database
motor>=3.3.0  # Async MongoDB driver
pymongo>=4.6.0

# Data validation and serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Authentication and security (for future use)
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# HTTP client
httpx>=0.25.0
requests>=2.31.0

# Logging
loguru>=0.7.0

# Configuration
python-dotenv>=1.0.0
pyyaml>=6.0.0

# Background tasks and async utilities
asyncio-mqtt>=0.13.0  # If needed for real-time updates
celery>=5.3.0  # Alternative for background tasks

# File handling and export
openpyxl>=3.1.0  # Excel export
aiofiles>=23.2.0  # Async file operations

# Text processing and utilities
python-slugify>=8.0.0
# difflib2>=0.1.0  # For content comparison

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx>=0.25.0  # For testing API endpoints

# Code quality
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.7.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Monitoring and metrics (optional)
prometheus-client>=0.19.0
psutil>=5.9.0

# Dependencies from existing components
# From Scraper
playwright>=1.40.0
fake-useragent>=1.4.0

# From AITextEnhancer  
google-generativeai>=0.3.0
tqdm>=4.65.0

# Additional utilities
Jinja2>=3.1.2
click>=8.1.0  # For CLI commands
rich>=13.7.0  # For beautiful console output
