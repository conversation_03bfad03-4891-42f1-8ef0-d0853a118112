import Link from 'next/link';
import OptimizedImage from '@/components/ui/OptimizedImage';
import { Story } from '@/types/story';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StoryCardProps {
  story: Story;
}

const StoryCard = ({ story }: StoryCardProps) => {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default'; // Green variant
      case 'ongoing':
        return 'secondary'; // Blue variant
      default:
        return 'outline'; // Yellow variant
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'ongoing':
        return 'Ongoing';
      default:
        return 'Paused';
    }
  };

  return (
    <Link href={`/stories/${story.id}`} className="group">
      <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-105 border-border/50 bg-card/50">
        <div className="relative aspect-[3/4] bg-muted">
          <OptimizedImage
            src={story.cover_image_url}
            alt={story.title}
            fill
            className="group-hover:scale-110 transition-transform duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={false}
            quality={80}
          />
          <div className="absolute top-2 right-2">
            <Badge variant={getStatusVariant(story.status)} className="text-xs">
              {getStatusText(story.status)}
            </Badge>
          </div>
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-foreground text-lg mb-2 group-hover:text-primary transition-colors overflow-hidden">
            <span className="block truncate">{story.title}</span>
          </h3>
          <p className="text-muted-foreground text-sm mb-2">
            Author: {story.author}
          </p>
          <p className="text-muted-foreground text-sm mb-3 overflow-hidden line-clamp-2">
            {story.description || 'No description available'}
          </p>
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>
              {story.total_chapters ? (
                `${story.total_chapters_scraped}/${story.total_chapters} chapters`
              ) : (
                `${story.total_chapters_scraped} chapters`
              )}
            </span>
            <span>{new Date(story.updated_at).toLocaleDateString('vi-VN')}</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default StoryCard;