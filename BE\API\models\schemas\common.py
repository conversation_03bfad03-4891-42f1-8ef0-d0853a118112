"""
Common API Models
"""

from datetime import datetime
from typing import List, Any
from pydantic import BaseModel, Field

# ============================================================================
# Common Response Models
# ============================================================================

class APIResponse(BaseModel):
    """Base API response model"""
    success: bool
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationInfo(BaseModel):
    """Pagination information"""
    page: int = 1
    page_size: int = 20
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool


class PaginatedResponse(APIResponse):
    """Paginated response base"""
    pagination: PaginationInfo
    data: List[Any]