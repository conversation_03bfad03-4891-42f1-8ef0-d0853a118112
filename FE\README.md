# WebTruyen Frontend

Ứng dụng Frontend hiện đại cho việc cào và đọ<PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON> xây dựng với Next.js 14, TypeScript và Tailwind CSS.

## Tính năng

- ✅ **Cào truyện**: Giao diện đơn giản để cào truyện từ các nguồn khác nhau
- ✅ **Quản lý truyện**: Hiển thị danh sách truyện đã cào với giao diện card đẹp mắt
- ✅ **Chi tiết truyện**: Trang thông tin chi tiết với danh sách chương
- ✅ **Đọc truyện**: Giao diện đọc tối ưu với các tùy chọn cá nhân hóa
- ✅ **Responsive Design**: Tối ưu cho mọi thiết bị (mobile, tablet, desktop)
- ✅ **Dark Theme**: <PERSON><PERSON><PERSON> tối hiện đại, dễ nhìn

## Công nghệ sử dụng

- **Next.js 14** với App Router
- **TypeScript** cho type safety
- **Tailwind CSS** cho styling
- **React Server Components** cho hiệu năng tối ưu

## Cài đặt

### Yêu cầu hệ thống

- Node.js 18+ 
- npm hoặc yarn

### Các bước cài đặt

1. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

2. **Cấu hình environment variables:**
   ```bash
   cp .env.example .env.local
   ```
   
   Chỉnh sửa `.env.local`:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:8000
   ```

3. **Chạy development server:**
   ```bash
   npm run dev
   ```

4. **Mở trình duyệt và truy cập:**
   ```
   http://localhost:3000
   ```

## Scripts

- `npm run dev` - Chạy development server
- `npm run build` - Build ứng dụng cho production
- `npm run start` - Chạy production server
- `npm run lint` - Kiểm tra linting

## Cấu trúc thư mục

```
src/
├── app/                    # App Router pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page
│   ├── loading.tsx       # Loading UI
│   ├── not-found.tsx     # 404 page
│   ├── scrape/           # Scraping pages
│   └── stories/          # Story pages
│       ├── [storyId]/    # Story detail
│       └── [storyId]/[chapterId]/ # Chapter reader
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── scrape/           # Scraping components
│   ├── story/            # Story components
│   └── reader/           # Reader components
├── services/             # API services
├── types/                # TypeScript types
└── utils/                # Utility functions
```

## API Integration

Ứng dụng tích hợp với Backend API thông qua các endpoints:

- `POST /scrape/story` - Cào truyện mới
- `GET /api/stories` - Lấy danh sách truyện
- `GET /api/stories/{id}` - Lấy chi tiết truyện
- `GET /api/stories/{id}/chapters` - Lấy danh sách chương
- `GET /api/stories/{id}/chapters/{chapterId}` - Lấy nội dung chương

## Tính năng nổi bật

### Giao diện đọc truyện
- Tùy chỉnh cỡ chữ (12px - 24px)
- Tùy chỉnh giãn dòng (1.2 - 2.5)
- Chế độ sáng/tối
- Điều hướng chương linh hoạt
- Dropdown chọn chương nhanh

### Responsive Design
- Mobile-first approach
- Grid layout tự động điều chỉnh
- Touch-friendly navigation
- Optimized images với Next.js Image

### Performance
- Server-side rendering với RSC
- Image optimization
- Lazy loading
- Efficient data fetching

## Development

### Code Style
- ESLint configuration
- TypeScript strict mode
- Tailwind CSS utilities
- Component-based architecture

### Best Practices
- Server Components cho static content
- Client Components cho interactive features
- Proper error handling
- Loading states
- SEO optimization

## Deployment

### Vercel (Recommended)
1. Push code lên GitHub
2. Connect repository với Vercel
3. Set environment variables
4. Deploy

### Manual Build
```bash
npm run build
npm run start
```

## Troubleshooting

### Common Issues

1. **API Connection Error:**
   - Kiểm tra `NEXT_PUBLIC_API_URL` trong `.env.local`
   - Đảm bảo Backend đang chạy

2. **Build Errors:**
   - Chạy `npm run lint` để kiểm tra lỗi
   - Kiểm tra TypeScript errors

3. **Styling Issues:**
   - Xóa `.next` folder và rebuild
   - Kiểm tra Tailwind configuration

## Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## License

MIT License