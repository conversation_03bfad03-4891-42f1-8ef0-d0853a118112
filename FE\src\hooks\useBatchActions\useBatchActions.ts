'use client';

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/components/ui/Toast';
import { 
  enhancedBatchScrapingService, 
  BatchProgressResponse, 
  BatchScrapeResponse
} from '@/services/enhancedBatchScrapingService';
import { 
  enhancementService, 
  EnhancementProgress,
  EnhancementResponse
} from '@/services/enhancementService';

interface BatchScrapingState {
  isActive: boolean;
  jobId?: string;
  progress: BatchProgressResponse | null;
}

interface BatchEnhancementState {
  isActive: boolean;
  jobId?: string;
  progress: EnhancementProgress | null;
}

export const useBatchActions = (onComplete: () => void) => {
  const { showSuccess, showError, showWarning, showProgress, updateToast, showInfo } = useToast();

  const [batchScrapingState, setBatchScrapingState] = useState<BatchScrapingState>({ isActive: false, progress: null });
  const [batchEnhancementState, setBatchEnhancementState] = useState<BatchEnhancementState>({ isActive: false, progress: null });

  useEffect(() => {
    return () => {
      enhancedBatchScrapingService.cleanup();
      enhancementService.cleanup();
    };
  }, []);

  const handleBatchScrape = useCallback(async (storyId: string, chapterIds: string[]) => {
    if (chapterIds.length === 0) {
      showWarning('No Chapters Selected', 'Please select at least one chapter to scrape.');
      return;
    }

    setBatchScrapingState({ isActive: true, progress: null });

    const progressToastId = showProgress('Starting Batch Scrape', `Scraping ${chapterIds.length} chapters...`, 0);

    try {
      const jobId = await enhancedBatchScrapingService.batchScrapeWithProgress(
        chapterIds,
        {
          onProgress: (progress: BatchProgressResponse) => {
            setBatchScrapingState(prev => ({ ...prev, progress }));
            updateToast(progressToastId, {
              title: `Scraping... ${Math.round(progress.progress_percentage)}%`,
              message: `${progress.completed_chapters}/${progress.total_chapters} chapters scraped.`,
              progress: progress.progress_percentage,
            });
          },
          onComplete: (response: BatchScrapeResponse) => {
            showSuccess('Batch Scrape Complete', `${response.successful_count} chapters processed.`);
            setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
            onComplete();
          },
          onError: (error: Error) => {
            showError('Batch Scrape Failed', error.message);
            setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
          }
        }
      );

      setBatchScrapingState(prev => ({ ...prev, jobId: jobId || undefined }));

    } catch (error: any) {
      showError('Batch Scrape Failed', error.message);
      setBatchScrapingState({ isActive: false, progress: null });
      updateToast(progressToastId, {
        title: 'Batch Scrape Failed',
        message: error.message,
        type: 'error',
      });
    }
  }, [showWarning, showProgress, updateToast, showError, showSuccess, onComplete]);

  const handleBatchEnhance = useCallback(async (storyId: string, chapterIds: string[]) => {
    if (chapterIds.length === 0) {
      showWarning('No Chapters Selected', 'Please select at least one chapter to enhance.');
      return;
    }

    setBatchEnhancementState({ isActive: true, progress: null });
    const progressToastId = showProgress('Starting Batch Enhancement', `Enhancing ${chapterIds.length} chapters...`, 0);

    try {
      await enhancementService.batchEnhanceWithProgress(chapterIds, {
        onProgress: (progress: EnhancementProgress) => {
          setBatchEnhancementState(prev => ({ ...prev, progress }));
          updateToast(progressToastId, {
            title: `Enhancing... ${Math.round(progress.progress_percentage)}%`,
            message: `${progress.completed_chapters}/${progress.total_chapters} chapters enhanced.`,
            progress: progress.progress_percentage,
          });
        },
        onComplete: (result: EnhancementResponse) => {
            showSuccess('Batch Enhancement Complete', `${result.successful_count} chapters were successfully enhanced.`);
            setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
            onComplete();
        },
        onError: (error: Error) => {
            showError('Batch Enhancement Failed', error.message);
            setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
        }
      });
    } catch (error: any) {
      showError('Batch Enhancement Failed', error.message);
      setBatchEnhancementState({ isActive: false, progress: null });
      updateToast(progressToastId, {
        title: 'Batch Enhancement Failed',
        message: error.message,
        type: 'error',
      });
    }
  }, [showWarning, showProgress, updateToast, showError, showSuccess, onComplete]);

  const cancelAction = (jobId?: string) => {
    if (!jobId) return;
    if (batchScrapingState.isActive && batchScrapingState.jobId) {
        enhancedBatchScrapingService.cancelBatchJob(batchScrapingState.jobId);
        setBatchScrapingState({ isActive: false, progress: null, jobId: undefined });
        showInfo('Batch Scraping Cancelled');
    } else if (batchEnhancementState.isActive && batchEnhancementState.jobId) {
        enhancementService.cancelEnhancementJob(batchEnhancementState.jobId);
        setBatchEnhancementState({ isActive: false, progress: null, jobId: undefined });
        showInfo('Batch Enhancement Cancelled');
    }
  };

  return {
    batchScrapingState,
    batchEnhancementState,
    handleBatchScrape,
    handleBatchEnhance,
    cancelAction,
  };
};
