#!/usr/bin/env python3
"""
Script to analyze content loss issues in enhanced files
"""

import os
from pathlib import Path

def analyze_content_loss():
    """Analyze content loss between original and enhanced files."""
    original_dir = Path('output')
    enhanced_dir = Path('enhanced_output')
    
    print('Content Loss Analysis')
    print('=' * 80)
    print(f'{"File":<50} {"Original":<8} {"Enhanced":<8} {"Ratio":<8} {"Status"}')
    print('-' * 80)
    
    issues = []
    
    for orig_file in sorted(original_dir.glob('*.md')):
        enhanced_file = enhanced_dir / (orig_file.stem + '_enhanced.md')
        
        if not enhanced_file.exists():
            print(f'{orig_file.name[:49]:<50} {"N/A":<8} {"MISSING":<8} {"N/A":<8} ❌')
            issues.append(f"Missing: {orig_file.name}")
            continue
        
        try:
            # Read both files
            with open(orig_file, 'r', encoding='utf-8') as f:
                orig_content = f.read()
            
            with open(enhanced_file, 'r', encoding='utf-8') as f:
                enhanced_content = f.read()
            
            orig_size = len(orig_content)
            enhanced_size = len(enhanced_content)
            ratio = enhanced_size / orig_size if orig_size > 0 else 0
            
            # Check for significant content loss
            if ratio < 0.7:
                status = '⚠️ LOSS'
                issues.append(f"Content loss: {orig_file.name} ({ratio:.1%})")
            elif ratio < 0.9:
                status = '⚠️ SHORT'
            else:
                status = '✅ OK'
            
            ratio_str = f"{ratio:.1%}"
            print(f'{orig_file.name[:49]:<50} {orig_size:<8} {enhanced_size:<8} {ratio_str:<8} {status}')
            
            # Check for truncation patterns
            if enhanced_content.endswith('"') and not orig_content.endswith('"'):
                issues.append(f"Possible truncation: {orig_file.name} (ends with quote)")
            
        except Exception as e:
            print(f'{orig_file.name[:49]:<50} {"ERROR":<8} {"ERROR":<8} {"N/A":<8} ❌')
            issues.append(f"Error reading: {orig_file.name} - {e}")
    
    print('\n' + '=' * 80)
    print('SUMMARY')
    print('=' * 80)
    
    if issues:
        print(f"Found {len(issues)} issues:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("No content loss issues detected!")
    
    return issues

if __name__ == "__main__":
    analyze_content_loss()
