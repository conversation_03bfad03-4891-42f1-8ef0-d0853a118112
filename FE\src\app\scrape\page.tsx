import Link from 'next/link';
import ScrapeForm from '@/components/scrape/ScrapeForm';

const ScrapePage = () => {
  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Web Novel Scraping</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Choose your scraping method: Simple scraping for quick results or Hierarchical scraping for comprehensive story processing.
          </p>
        </div>

        {/* Scraping Options */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Simple Scraping */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-center mb-6">
              <div className="text-4xl mb-4">⚡</div>
              <h2 className="text-2xl font-bold text-white mb-2">Simple Scraping</h2>
              <p className="text-gray-400">
                Quick and straightforward story scraping using the legacy workflow.
              </p>
            </div>
            
            <div className="space-y-3 text-sm text-gray-300 mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Fast story information extraction</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Basic chapter URL collection</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Simple progress tracking</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">~</span>
                <span>Limited batch operations</span>
              </div>
            </div>
            
            <ScrapeForm />
          </div>

          {/* Hierarchical Scraping */}
          <div className="bg-gray-800 rounded-lg p-6 border border-indigo-500">
            <div className="text-center mb-6">
              <div className="text-4xl mb-4">🚀</div>
              <h2 className="text-2xl font-bold text-white mb-2">Hierarchical Scraping</h2>
              <p className="text-gray-400">
                Advanced scraping with Story → Page → Chapter workflow and comprehensive management.
              </p>
            </div>
            
            <div className="space-y-3 text-sm text-gray-300 mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Complete story hierarchy mapping</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Advanced progress tracking</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Batch chapter content scraping</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Comprehensive statistics</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Rate limiting & error handling</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-green-400">✓</span>
                <span>Visual workflow management</span>
              </div>
            </div>
            
            <Link
              href="/scrape/hierarchical"
              className="block w-full py-3 px-6 bg-indigo-600 text-white text-center rounded-md font-medium hover:bg-indigo-700 transition-colors focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            >
              Open Hierarchical Dashboard
            </Link>
          </div>
        </div>

        {/* Workflow Comparison */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-6 text-center">Workflow Comparison</h3>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Simple Workflow */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <span>⚡</span>
                <span>Simple Workflow</span>
              </h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <span className="text-gray-300">Extract story information</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                  <span className="text-gray-300">Collect chapter URLs</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                  <span className="text-gray-300">Store in database</span>
                </div>
              </div>
            </div>

            {/* Hierarchical Workflow */}
            <div>
              <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <span>🚀</span>
                <span>Hierarchical Workflow</span>
              </h4>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <span className="text-gray-300">Story info scraping & page URL generation</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                  <span className="text-gray-300">Page processing & chapter URL extraction</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                  <span className="text-gray-300">Hierarchy storage & organization</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">4</div>
                  <span className="text-gray-300">Batch chapter content scraping</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">5</div>
                  <span className="text-gray-300">Progress tracking & management</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Overview */}
        <div className="mt-12 bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-bold text-white mb-6 text-center">Key Features</h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-3">📊</div>
              <h4 className="font-semibold text-white mb-2">Progress Tracking</h4>
              <p className="text-gray-400 text-sm">
                Real-time progress indicators for all scraping phases with detailed status updates.
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-3">⚙️</div>
              <h4 className="font-semibold text-white mb-2">Batch Operations</h4>
              <p className="text-gray-400 text-sm">
                Efficient batch processing with rate limiting and concurrent operation management.
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-3">🛡️</div>
              <h4 className="font-semibold text-white mb-2">Error Handling</h4>
              <p className="text-gray-400 text-sm">
                Comprehensive error handling with user-friendly messages and recovery options.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrapePage;