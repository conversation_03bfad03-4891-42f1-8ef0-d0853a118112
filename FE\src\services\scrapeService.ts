import { ScrapeRequest, ScrapeResponse } from '@/types/scrape';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

export const scrapeStory = async (url: string): Promise<ScrapeResponse> => {
  try {
    // Use the new hierarchical scraping endpoint
    const response = await fetch(`${API_BASE_URL}/api/v1/hierarchical/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        story_url: url,
        max_pages: 10,
        scrape_content: false  // Only scrape structure, not content
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      message: data.message || 'Story scraped successfully',
      storyId: data.story_id
    };
  } catch (error) {
    console.error('Error scraping story:', error);
    throw error;
  }
};

// New function for hierarchical story scraping with content
export const scrapeStoryWithContent = async (url: string, maxPages: number = 10): Promise<ScrapeResponse> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/hierarchical/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        story_url: url,
        max_pages: maxPages,
        scrape_content: true  // Also scrape chapter content
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      message: data.message || 'Story and content scraped successfully',
      storyId: data.story_id
    };
  } catch (error) {
    console.error('Error scraping story with content:', error);
    throw error;
  }
};