"""Page Model for Story → Page → Chapter Hierarchy"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, HttpUrl
from bson import ObjectId
from .database import PyObjectId, ScrapingStatus


class PageMetadata(BaseModel):
    """Page metadata and scraping information"""
    scraped_at: datetime = Field(default_factory=datetime.utcnow)
    scraping_duration: Optional[float] = None  # seconds
    page_number: int
    total_chapters_on_page: int = 0
    scraping_errors: List[str] = Field(default_factory=list)
    

class Page(BaseModel):
    """Page document model for Story → Page → Chapter hierarchy"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    story_id: PyObjectId
    page_number: int
    page_url: HttpUrl
    
    # Chapter information extracted from this page
    chapter_urls: List[str] = Field(default_factory=list)
    total_chapters_on_page: int = 0
    
    # Scraping status
    is_scraped: bool = False
    scraping_status: ScrapingStatus = ScrapingStatus.PENDING
    
    # Metadata
    metadata: Optional[PageMetadata] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True