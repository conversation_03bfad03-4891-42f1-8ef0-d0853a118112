'use client';

import { useState } from 'react';
import { scrapeStory } from '@/services/scrapeService';

const ScrapeForm = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!url.trim()) {
      setMessage('Please enter a valid URL');
      setIsError(true);
      return;
    }

    setLoading(true);
    setMessage('');
    setIsError(false);

    try {
      const response = await scrapeStory(url.trim());
      setMessage(response.message);
      setUrl('');
      
      // Redirect to story page if storyId is available
      if (response.storyId) {
        setTimeout(() => {
          window.location.href = `/stories/${response.storyId}`;
        }, 2000);
      }
    } catch (error) {
      setMessage(error instanceof Error ? error.message : 'An error occurred while scraping the story.');
      setIsError(true);
      console.error('Scraping error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-8 max-w-md">
      <form onSubmit={handleSubmit}>
        <div className="flex flex-col space-y-4">
          <label htmlFor="url" className="sr-only">
            Story URL
          </label>
          <input
            id="url"
            name="url"
            type="url"
            required
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="block w-full rounded-md border-gray-600 bg-zinc-700 px-4 py-3 text-white placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="https://example.com/story/123"
          />
          <button
            type="submit"
            disabled={loading}
            className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Scraping...' : 'Scrape Story'}
          </button>
        </div>
      </form>
      {message && (
        <p
          className={`mt-4 text-center text-sm font-medium ${isError ? 'text-red-400' : 'text-green-400'}`}>
          {message}
        </p>
      )}
    </div>
  );
};

export default ScrapeForm;