"""
Logging Middleware for Request/Response Tracking

This middleware provides comprehensive logging for all API requests and responses,
including timing, status codes, and error tracking.
"""

import time
import logging
import json
from typing import Callable
from datetime import datetime

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import StreamingResponse


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses"""
    
    def __init__(self, app, logger_name: str = "api.requests"):
        super().__init__(app)
        self.logger = logging.getLogger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details"""
        start_time = time.time()
        
        # Extract request information
        request_info = await self._extract_request_info(request)
        
        # Log incoming request
        self.logger.info(
            f"🔵 {request_info['method']} {request_info['path']} - "
            f"Client: {request_info['client_ip']} - "
            f"User-Agent: {request_info['user_agent'][:100]}..."
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Extract response information
            response_info = self._extract_response_info(response, process_time)
            
            # Log response
            status_emoji = self._get_status_emoji(response.status_code)
            self.logger.info(
                f"{status_emoji} {request_info['method']} {request_info['path']} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s - "
                f"Size: {response_info['content_length']}"
            )
            
            # Add timing header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # Calculate processing time for errors
            process_time = time.time() - start_time
            
            # Log error
            self.logger.error(
                f"❌ {request_info['method']} {request_info['path']} - "
                f"Error: {str(e)} - "
                f"Time: {process_time:.3f}s",
                exc_info=True
            )
            
            raise
    
    async def _extract_request_info(self, request: Request) -> dict:
        """Extract relevant information from request"""
        # Get client IP (handle proxy headers)
        client_ip = request.client.host if request.client else "unknown"
        
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        # Extract other headers
        user_agent = request.headers.get("User-Agent", "unknown")
        content_type = request.headers.get("Content-Type", "")
        content_length = request.headers.get("Content-Length", "0")
        
        # Extract query parameters
        query_params = dict(request.query_params)
        
        return {
            "method": request.method,
            "path": str(request.url.path),
            "query_params": query_params,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "content_type": content_type,
            "content_length": content_length,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def _extract_response_info(self, response: Response, process_time: float) -> dict:
        """Extract relevant information from response"""
        content_length = response.headers.get("Content-Length", "unknown")
        content_type = response.headers.get("Content-Type", "unknown")
        
        return {
            "status_code": response.status_code,
            "content_type": content_type,
            "content_length": content_length,
            "process_time": process_time
        }
    
    def _get_status_emoji(self, status_code: int) -> str:
        """Get emoji based on HTTP status code"""
        if 200 <= status_code < 300:
            return "✅"
        elif 300 <= status_code < 400:
            return "🔄"
        elif 400 <= status_code < 500:
            return "⚠️"
        elif 500 <= status_code < 600:
            return "❌"
        else:
            return "❓"


class StructuredLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured JSON logging"""
    
    def __init__(self, app, logger_name: str = "api.structured"):
        super().__init__(app)
        self.logger = logging.getLogger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with structured logging"""
        start_time = time.time()
        request_id = self._generate_request_id()
        
        # Add request ID to request state
        request.state.request_id = request_id
        
        # Extract request data
        request_data = await self._extract_request_data(request)
        
        # Log structured request
        self.logger.info(json.dumps({
            "event": "request_started",
            "request_id": request_id,
            "timestamp": datetime.utcnow().isoformat(),
            **request_data
        }))
        
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Extract response data
            response_data = self._extract_response_data(response, process_time)
            
            # Log structured response
            self.logger.info(json.dumps({
                "event": "request_completed",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "process_time": process_time,
                **request_data,
                **response_data
            }))
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            # Log structured error
            self.logger.error(json.dumps({
                "event": "request_failed",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat(),
                "process_time": process_time,
                "error": str(e),
                "error_type": type(e).__name__,
                **request_data
            }))
            
            raise
    
    async def _extract_request_data(self, request: Request) -> dict:
        """Extract structured request data"""
        return {
            "method": request.method,
            "path": str(request.url.path),
            "query_string": str(request.url.query),
            "client_ip": self._get_client_ip(request),
            "user_agent": request.headers.get("User-Agent", ""),
            "content_type": request.headers.get("Content-Type", ""),
            "content_length": int(request.headers.get("Content-Length", 0))
        }
    
    def _extract_response_data(self, response: Response, process_time: float) -> dict:
        """Extract structured response data"""
        return {
            "status_code": response.status_code,
            "response_content_type": response.headers.get("Content-Type", ""),
            "response_content_length": response.headers.get("Content-Length", ""),
            "process_time": process_time
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address handling proxy headers"""
        # Check forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID"""
        import uuid
        return str(uuid.uuid4())[:8]
