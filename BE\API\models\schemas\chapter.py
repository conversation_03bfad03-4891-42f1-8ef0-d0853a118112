"""
Chapter related API Models
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, HttpUrl

from ..database import EnhancementStatus
from .common import APIResponse, PaginatedResponse

# ============================================================================
# Chapter Data Retrieval API Models
# ============================================================================

class ChapterListRequest(BaseModel):
    """Request for chapter list"""
    story_id: str
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=50, ge=1, le=200)
    enhanced_only: bool = False
    scraped_only: bool = False


class ChapterListItem(BaseModel):
    """Chapter item in list response"""
    id: str
    chapter_number: int
    title: str
    url: HttpUrl
    is_scraped: bool
    is_enhanced: bool
    enhancement_status: EnhancementStatus
    word_count: Optional[int] = None
    created_at: datetime
    updated_at: datetime


class ChapterListResponse(PaginatedResponse):
    """Response for chapter list"""
    data: List[ChapterListItem]


class ChapterContentResponse(APIResponse):
    """Response for chapter content"""
    id: str
    story_id: str
    chapter_number: int
    title: str
    url: HttpUrl
    original_content: Optional[str] = None
    enhanced_content: Optional[str] = None
    is_enhanced: bool
    enhancement_status: EnhancementStatus
    word_count: Optional[int] = None
    enhancement_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime