"""
Models Package

This package contains data models for the API application.
"""

from .database import (
    Story, Chapter, Page, ScrapingJob, EnhancementJob,
    StoryStatus, ScrapingStatus, EnhancementStatus,
    db_manager, DatabaseManager
)
from .schemas import (
    APIResponse, PaginationInfo, PaginatedResponse,
    StoryInfoRequest, StoryInfoResponse,
    BatchChapterScrapeRequest, BatchChapterScrapeResponse,
    BatchEnhancementRequest, BatchEnhancementResponse,
    StoryListRequest, StoryListResponse,
    ChapterContentResponse, JobStatusResponse
)

__all__ = [
    # Database models
    "Story", "Chapter", "Page", "ScrapingJob", "EnhancementJob",
    "StoryStatus", "ScrapingStatus", "EnhancementStatus",
    "db_manager", "DatabaseManager",
    
    # API models
    "APIResponse", "PaginationInfo", "PaginatedResponse",
    "StoryInfoRequest", "StoryInfoResponse",
    "BatchChapterScrapeRequest", "BatchChapterScrapeResponse", 
    "BatchEnhancementRequest", "BatchEnhancementResponse",
    "StoryListRequest", "StoryListResponse",
    "ChapterContentResponse", "JobStatusResponse"
]
