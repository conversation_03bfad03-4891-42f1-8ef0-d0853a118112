'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';
import { BookOpen, Search, Filter, Loader2 } from 'lucide-react';

import { Chapter } from '@/types/story';
import { useChaptersCache } from '@/hooks/useChaptersCache';

// -----------------------------------------------------------------------------
// Types
// -----------------------------------------------------------------------------

type StatusFilter = 'all' | 'scraped' | 'unscraped' | 'enhanced' | 'unenhanced';

interface ChapterListModalProps {
  storyId: string;
  currentChapter: Chapter;
  className?: string;
}

// -----------------------------------------------------------------------------
// Component
// -----------------------------------------------------------------------------

const ChapterListModal: React.FC<ChapterListModalProps> = ({
  storyId,
  currentChapter,
  className = ''
}) => {
  // UI state
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<StatusFilter>('all');

  // Data
  const {
    chapters,
    loading,
    error,
    totalChapters,
    loadAllChapters
  } = useChaptersCache(storyId);

  // Fetch chapters when modal opens
  useEffect(() => {
    if (open && chapters.length === 0) {
      loadAllChapters(10); // fetch up to 1 000 chapters
    }
  }, [open, chapters.length, loadAllChapters]);

  // Derived list
  const filteredChapters = useMemo(() => {
    return chapters.filter((ch) => {
      // search
      if (search) {
        const q = search.toLowerCase();
        if (
          !ch.chapter_number.toString().includes(q) &&
          !(ch.title ?? '').toLowerCase().includes(q)
        )
          return false;
      }
      // status
      switch (status) {
        case 'scraped':
          return ch.is_scraped;
        case 'unscraped':
          return !ch.is_scraped;
        case 'enhanced':
          return ch.is_enhanced;
        case 'unenhanced':
          return ch.is_scraped && !ch.is_enhanced;
        default:
          return true;
      }
    });
  }, [chapters, search, status]);

  // Navigation
  const router = useRouter();
  const goToChapter = (num: number) => {
    router.push(`/stories/${storyId}/${num}`);
    setOpen(false);
  };

  // Helpers
  const renderStatusBadges = (ch: Chapter) => (
    <>
      {ch.is_scraped && (
        <Badge
          variant="secondary"
          className="bg-green-500/20 text-green-400 text-xs"
        >
          Scraped
        </Badge>
      )}
      {ch.is_enhanced && (
        <Badge
          variant="secondary"
          className="bg-blue-500/20 text-blue-400 text-xs"
        >
          Enhanced
        </Badge>
      )}
    </>
  );

  // ---------------------------------------------------------------------------
  // Render
  // ---------------------------------------------------------------------------

  return (
    <div className={className}>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="lg" className="gap-2">
            <BookOpen size={16} /> Table of Contents
          </Button>
        </DialogTrigger>

        <DialogContent className="sm:max-w-4xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BookOpen size={18} />
              Table of Contents – {totalChapters || '...'} chapters
            </DialogTitle>
          </DialogHeader>

          {/* Controls */}
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by number or title..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value as StatusFilter)}
                className="px-3 py-1 border rounded-md text-sm bg-white dark:bg-gray-800 dark:border-gray-600"
              >
                <option value="all">All chapters</option>
                <option value="scraped">Scraped</option>
                <option value="unscraped">Not scraped</option>
                <option value="enhanced">Enhanced</option>
                <option value="unenhanced">Not enhanced</option>
              </select>
            </div>
          </div>

          {/* Body */}
          {loading && chapters.length === 0 && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" /> Loading...
            </div>
          )}
          {error && (
            <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-4 text-red-400 text-center">
              {error}
            </div>
          )}

          {!loading && (
            <div className="flex-1 overflow-y-auto pr-2 grid grid-cols-1 md:grid-cols-2 gap-2">
              {filteredChapters.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  No chapters match the {search ? `"${search}"` : 'current'} filter.
                </div>
              ) : (
                filteredChapters.map((ch) => (
                  <button
                    key={ch.id}
                    onClick={() => goToChapter(ch.chapter_number)}
                    className={`text-left p-3 rounded-lg border transition-all duration-200 ${
                      ch.id === currentChapter.id
                        ? 'bg-indigo-600/20 border-indigo-500 text-indigo-300'
                        : 'bg-zinc-800/50 border-zinc-700/50 text-gray-300 hover:bg-zinc-700/50 hover:border-zinc-600'
                    }`}
                  >
                    <div className="flex justify-between mb-2">
                      <span className="font-medium text-sm">
                        Chapter {ch.chapter_number}
                      </span>
                      {ch.id === currentChapter.id && (
                        <Badge
                          variant="secondary"
                          className="bg-indigo-500/20 text-indigo-400 text-xs"
                        >
                          Current
                        </Badge>
                      )}
                    </div>

                    {ch.title && (
                      <div className="text-sm text-gray-400 mb-2 line-clamp-2">
                        {ch.title}
                      </div>
                    )}

                    <div className="flex gap-1 flex-wrap">
                      {renderStatusBadges(ch)}
                    </div>
                  </button>
                ))
              )}
            </div>
          )}

          {!loading && chapters.length > 0 && (
            <div className="border-t border-zinc-700 pt-3 text-sm text-gray-400 text-center">
              Showing {filteredChapters.length}/{chapters.length} chapters
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChapterListModal;
