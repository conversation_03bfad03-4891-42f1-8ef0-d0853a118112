#!/usr/bin/env python3
"""
Batch Chapter Enhancement Script

This script provides intelligent batch processing for story chapters with:
- Smart retry logic for failed files
- Content validation and quality checks
- Progress tracking and resumption
- Fallback strategies for problematic content
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

from text_enhancer import TextEnhancer
from analyze_content_loss import analyze_content_loss

@dataclass
class ProcessingState:
    """Track processing state for resumption."""
    total_files: int
    processed_files: List[str]
    failed_files: List[str]
    skipped_files: List[str]
    start_time: str
    last_update: str

class BatchChapterEnhancer:
    """Intelligent batch processor for story chapters."""
    
    def __init__(self, input_dir: str = "output", output_dir: str = "enhanced_output"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.state_file = Path("batch_processing_state.json")
        
        # Processing settings
        self.max_retries_per_file = 2
        self.min_success_ratio = 0.7  # Minimum content ratio to consider success
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging configuration."""
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('batch_enhancement.log'),
                logging.StreamHandler()
            ]
        )
    
    def _load_state(self) -> Optional[ProcessingState]:
        """Load previous processing state if exists."""
        if not self.state_file.exists():
            return None
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return ProcessingState(**data)
        except Exception as e:
            logging.warning(f"Could not load previous state: {e}")
            return None
    
    def _save_state(self, state: ProcessingState):
        """Save current processing state."""
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(state), f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Could not save state: {e}")
    
    def _get_files_to_process(self) -> List[Path]:
        """Get list of markdown files to process."""
        return sorted(self.input_dir.glob("*.md"))
    
    def _validate_enhancement(self, original_file: Path, enhanced_file: Path) -> bool:
        """Validate that enhancement was successful."""
        if not enhanced_file.exists():
            return False
        
        try:
            with open(original_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            with open(enhanced_file, 'r', encoding='utf-8') as f:
                enhanced_content = f.read()
            
            original_length = len(original_content.strip())
            enhanced_length = len(enhanced_content.strip())
            
            if original_length == 0:
                return enhanced_length > 0
            
            ratio = enhanced_length / original_length
            
            # Check for quality indicators
            is_valid = (
                ratio >= self.min_success_ratio and  # Sufficient content preserved
                enhanced_length > 100 and  # Minimum meaningful length
                not enhanced_content.strip().endswith('"') or original_content.strip().endswith('"') and  # No unexpected truncation
                enhanced_content.count('\n') >= original_content.count('\n') * 0.5  # Reasonable line count
            )
            
            if not is_valid:
                logging.warning(f"Enhancement validation failed for {original_file.name}: "
                              f"ratio={ratio:.2f}, length={enhanced_length}")
            
            return is_valid
            
        except Exception as e:
            logging.error(f"Error validating {original_file.name}: {e}")
            return False
    
    def _process_single_file(self, file_path: Path, enhancer: TextEnhancer) -> bool:
        """Process a single file with validation."""
        enhanced_file = self.output_dir / (file_path.stem + "_enhanced.md")
        
        for attempt in range(self.max_retries_per_file):
            try:
                logging.info(f"Processing {file_path.name} (attempt {attempt + 1}/{self.max_retries_per_file})")
                
                # Remove existing enhanced file if it exists
                if enhanced_file.exists():
                    enhanced_file.unlink()
                
                # Process the file
                result = enhancer.process_file(file_path)
                
                if result.success and result.enhanced:
                    # Validate the result
                    if self._validate_enhancement(file_path, enhanced_file):
                        logging.info(f"✅ Successfully processed: {file_path.name}")
                        return True
                    else:
                        logging.warning(f"⚠️ Enhancement validation failed: {file_path.name}")
                        if attempt < self.max_retries_per_file - 1:
                            continue
                elif result.success and not result.enhanced:
                    # File was processed but not enhanced (e.g., .txt file or AI unavailable)
                    logging.info(f"📄 Processed without enhancement: {file_path.name}")
                    return True
                else:
                    logging.error(f"❌ Processing failed: {file_path.name} - {result.error_message}")
                    if attempt < self.max_retries_per_file - 1:
                        time.sleep(5)  # Wait before retry
                        continue
                
            except Exception as e:
                logging.error(f"❌ Unexpected error processing {file_path.name}: {e}")
                if attempt < self.max_retries_per_file - 1:
                    time.sleep(5)
                    continue
        
        return False
    
    def process_all(self, resume: bool = True, force_reprocess: bool = False) -> Dict[str, any]:
        """Process all files with intelligent retry and validation."""
        # Load previous state if resuming
        state = None
        if resume and not force_reprocess:
            state = self._load_state()
        
        files_to_process = self._get_files_to_process()
        
        if not files_to_process:
            logging.warning("No markdown files found to process!")
            return {"total": 0, "processed": 0, "failed": 0, "skipped": 0}
        
        # Initialize or resume state
        if state and not force_reprocess:
            logging.info(f"Resuming previous session started at {state.start_time}")
            processed_files = set(state.processed_files)
            failed_files = set(state.failed_files)
            skipped_files = set(state.skipped_files)
        else:
            logging.info("Starting new processing session")
            processed_files = set()
            failed_files = set()
            skipped_files = set()
        
        # Filter files to process
        remaining_files = [f for f in files_to_process 
                          if f.name not in processed_files and f.name not in skipped_files]
        
        if not remaining_files:
            logging.info("All files have been processed!")
            return {
                "total": len(files_to_process),
                "processed": len(processed_files),
                "failed": len(failed_files),
                "skipped": len(skipped_files)
            }
        
        logging.info(f"Processing {len(remaining_files)} files...")
        
        # Initialize enhancer
        enhancer = TextEnhancer(
            input_path="dummy",  # Will be overridden per file
            output_dir=str(self.output_dir),
            ai_enabled=True,
            preserve_structure=False,
            suffix="_enhanced",
            skip_existing=False  # We handle this ourselves
        )
        
        # Process files
        current_state = ProcessingState(
            total_files=len(files_to_process),
            processed_files=list(processed_files),
            failed_files=list(failed_files),
            skipped_files=list(skipped_files),
            start_time=datetime.now().isoformat(),
            last_update=datetime.now().isoformat()
        )
        
        for i, file_path in enumerate(remaining_files, 1):
            logging.info(f"\n[{i}/{len(remaining_files)}] Processing: {file_path.name}")
            
            try:
                success = self._process_single_file(file_path, enhancer)
                
                if success:
                    processed_files.add(file_path.name)
                    current_state.processed_files = list(processed_files)
                else:
                    failed_files.add(file_path.name)
                    current_state.failed_files = list(failed_files)
                
                # Update state
                current_state.last_update = datetime.now().isoformat()
                self._save_state(current_state)
                
                # Brief pause between files
                time.sleep(1)
                
            except KeyboardInterrupt:
                logging.info("Processing interrupted by user. State saved.")
                break
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                failed_files.add(file_path.name)
                current_state.failed_files = list(failed_files)
                self._save_state(current_state)
        
        # Final summary
        summary = {
            "total": len(files_to_process),
            "processed": len(processed_files),
            "failed": len(failed_files),
            "skipped": len(skipped_files)
        }
        
        logging.info(f"\n🎉 Processing complete!")
        logging.info(f"Total files: {summary['total']}")
        logging.info(f"Successfully processed: {summary['processed']}")
        logging.info(f"Failed: {summary['failed']}")
        logging.info(f"Skipped: {summary['skipped']}")
        
        if failed_files:
            logging.info(f"\n❌ Failed files:")
            for filename in sorted(failed_files):
                logging.info(f"  - {filename}")
        
        return summary

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Batch enhance story chapters")
    parser.add_argument("--input", default="output", help="Input directory")
    parser.add_argument("--output", default="enhanced_output", help="Output directory")
    parser.add_argument("--no-resume", action="store_true", help="Don't resume previous session")
    parser.add_argument("--force", action="store_true", help="Force reprocess all files")
    parser.add_argument("--analyze", action="store_true", help="Analyze content loss after processing")
    
    args = parser.parse_args()
    
    enhancer = BatchChapterEnhancer(args.input, args.output)
    
    try:
        summary = enhancer.process_all(
            resume=not args.no_resume,
            force_reprocess=args.force
        )
        
        if args.analyze:
            print("\n" + "="*60)
            print("CONTENT LOSS ANALYSIS")
            print("="*60)
            analyze_content_loss()
        
        # Exit with appropriate code
        sys.exit(0 if summary['failed'] == 0 else 1)
        
    except Exception as e:
        logging.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
