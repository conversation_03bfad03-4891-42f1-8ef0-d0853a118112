#!/usr/bin/env python3
"""
Text Enhancer <PERSON> - Improves writing style and quality of text files

This script processes .md and .txt files to enhance their writing quality using AI integration
with Vietnamese language context. It handles batch processing, preserves file structure,
and provides robust error handling with graceful fallbacks.

Features:
- Batch processing of multiple files
- AI enhancement for .md files with Vietnamese context
- Preserves original file structure and formatting
- Graceful rate limit handling and fallback behavior
- Clear progress feedback and comprehensive error handling
"""

import os
import sys
import time
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from tqdm import tqdm
import shutil
import concurrent.futures
from threading import Lock

# Import the existing AI enhancer
from ai_enhancer import GeminiTextEnhancer


@dataclass
class ProcessingResult:
    """Result of processing a single file."""
    file_path: Path
    success: bool
    enhanced: bool
    output_path: Optional[Path] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0


class TextEnhancer:
    """
    Main class for enhancing text files with AI integration.
    """
    
    def __init__(self,
                 input_path: str,
                 output_dir: str = "enhanced_output",
                 ai_enabled: bool = True,
                 preserve_structure: bool = True,
                 suffix: str = "_enhanced",
                 max_workers: int = 1,  # Conservative default for AI processing
                 skip_existing: bool = True):
        """
        Initialize the Text Enhancer.

        Args:
            input_path: Path to input file or directory
            output_dir: Directory to save enhanced files
            ai_enabled: Whether to use AI enhancement (only for .md files)
            preserve_structure: Whether to preserve directory structure
            suffix: Suffix to add to enhanced filenames
            max_workers: Maximum number of concurrent workers (1 for AI to avoid rate limits)
            skip_existing: Skip files that already exist in output directory
        """
        self.input_path = Path(input_path)
        self.output_dir = Path(output_dir)
        self.ai_enabled = ai_enabled
        self.preserve_structure = preserve_structure
        self.suffix = suffix
        self.max_workers = max_workers
        self.skip_existing = skip_existing

        # Supported file extensions
        self.supported_extensions = {'.md', '.txt'}

        # Thread safety
        self.file_lock = Lock()

        # Initialize AI enhancer (only for .md files)
        self.ai_enhancer = None
        if self.ai_enabled:
            try:
                self.ai_enhancer = GeminiTextEnhancer()
                if not self.ai_enhancer.is_available():
                    logging.warning("AI enhancer not available. Will process files without AI enhancement.")
                    self.ai_enhancer = None
            except Exception as e:
                logging.warning(f"Failed to initialize AI enhancer: {e}. Will process files without AI enhancement.")
                self.ai_enhancer = None

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_file = self.output_dir / "enhancement_log.txt"
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Configure logger
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    def find_files(self) -> List[Path]:
        """
        Find all supported files in the input path.
        
        Returns:
            List of file paths to process
        """
        files = []
        
        if self.input_path.is_file():
            if self.input_path.suffix.lower() in self.supported_extensions:
                files.append(self.input_path)
            else:
                logging.warning(f"Unsupported file type: {self.input_path.suffix}")
        elif self.input_path.is_dir():
            for ext in self.supported_extensions:
                files.extend(self.input_path.rglob(f"*{ext}"))
        else:
            raise FileNotFoundError(f"Input path does not exist: {self.input_path}")
        
        return sorted(files)
    
    def _get_output_path(self, input_file: Path) -> Path:
        """
        Generate output path for a given input file.
        
        Args:
            input_file: Input file path
            
        Returns:
            Output file path
        """
        if self.preserve_structure and self.input_path.is_dir():
            # Preserve directory structure
            relative_path = input_file.relative_to(self.input_path)
            output_path = self.output_dir / relative_path
        else:
            # Flat structure
            output_path = self.output_dir / input_file.name
        
        # Add suffix to filename
        stem = output_path.stem
        suffix = output_path.suffix
        output_path = output_path.parent / f"{stem}{self.suffix}{suffix}"
        
        # Create parent directory if needed
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        return output_path
    
    def _read_file_content(self, file_path: Path) -> Tuple[str, str]:
        """
        Read file content and extract title if it's a markdown file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Tuple of (title, content)
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract title for markdown files
            title = file_path.stem
            if file_path.suffix.lower() == '.md':
                lines = content.split('\n')
                for line in lines:
                    if line.strip().startswith('# '):
                        title = line.strip()[2:].strip()
                        break
            
            return title, content
            
        except Exception as e:
            raise IOError(f"Failed to read file {file_path}: {e}")
    
    def _enhance_content(self, title: str, content: str, file_extension: str) -> Tuple[str, bool]:
        """
        Enhance content using AI if applicable.
        
        Args:
            title: File title
            content: File content
            file_extension: File extension
            
        Returns:
            Tuple of (enhanced_content, was_enhanced)
        """
        # Only enhance .md files with AI
        if (file_extension.lower() == '.md' and 
            self.ai_enhancer and 
            self.ai_enhancer.is_available()):
            
            try:
                enhanced = self.ai_enhancer.enhance_text(title, content)
                if enhanced:
                    return enhanced, True
                else:
                    logging.warning(f"AI enhancement failed for: {title}")
                    return content, False
            except Exception as e:
                logging.error(f"AI enhancement error for {title}: {e}")
                return content, False
        
        # For .txt files or when AI is not available, return original content
        return content, False
    
    def _write_enhanced_file(self, output_path: Path, content: str) -> None:
        """
        Write enhanced content to output file.
        
        Args:
            output_path: Output file path
            content: Content to write
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            raise IOError(f"Failed to write file {output_path}: {e}")
    
    def _should_skip_file(self, file_path: Path) -> bool:
        """Check if file should be skipped based on existing output."""
        if not self.skip_existing:
            return False

        output_path = self._get_output_path(file_path)
        if output_path.exists():
            # Check if output is newer than input
            try:
                input_mtime = file_path.stat().st_mtime
                output_mtime = output_path.stat().st_mtime
                if output_mtime > input_mtime:
                    return True
            except OSError:
                pass
        return False

    def process_file(self, file_path: Path) -> ProcessingResult:
        """
        Process a single file with improved error handling and validation.

        Args:
            file_path: Path to the file to process

        Returns:
            ProcessingResult object
        """
        start_time = time.time()
        result = ProcessingResult(file_path=file_path, success=False, enhanced=False)

        try:
            # Check if we should skip this file
            if self._should_skip_file(file_path):
                result.success = True
                result.enhanced = False
                result.output_path = self._get_output_path(file_path)
                result.processing_time = time.time() - start_time
                logging.info(f"⏭️ Skipped (already exists): {file_path.name}")
                return result

            # Read file content
            title, content = self._read_file_content(file_path)

            # Validate content
            if not content.strip():
                logging.warning(f"Empty content in file: {file_path.name}")
                result.success = True
                result.enhanced = False
                result.output_path = self._get_output_path(file_path)
                result.processing_time = time.time() - start_time
                return result

            # Get output path
            output_path = self._get_output_path(file_path)

            # Enhance content
            enhanced_content, was_enhanced = self._enhance_content(
                title, content, file_path.suffix
            )

            # Validate enhanced content
            if enhanced_content and len(enhanced_content.strip()) > 0:
                # Thread-safe file writing
                with self.file_lock:
                    self._write_enhanced_file(output_path, enhanced_content)

                # Update result
                result.success = True
                result.enhanced = was_enhanced
                result.output_path = output_path
                result.processing_time = time.time() - start_time

                logging.info(f"✅ Processed: {file_path.name} -> {output_path.name} "
                            f"(Enhanced: {was_enhanced}, Time: {result.processing_time:.2f}s)")
            else:
                raise ValueError("Enhanced content is empty or invalid")

        except Exception as e:
            result.error_message = str(e)
            result.processing_time = time.time() - start_time
            logging.error(f"❌ Failed to process {file_path.name}: {e}")

        return result
    
    def process_all(self) -> Dict[str, any]:
        """
        Process all files with optimized batch processing and return summary statistics.

        Returns:
            Dictionary containing processing statistics
        """
        logging.info(f"🚀 Starting text enhancement process...")
        logging.info(f"Input: {self.input_path}")
        logging.info(f"Output: {self.output_dir}")
        logging.info(f"AI Enhancement: {'Enabled' if self.ai_enhancer else 'Disabled'}")
        logging.info(f"Max Workers: {self.max_workers}")
        logging.info(f"Skip Existing: {self.skip_existing}")

        # Find files to process
        files = self.find_files()
        if not files:
            logging.warning("No supported files found to process.")
            return {"total_files": 0, "processed": 0, "enhanced": 0, "failed": 0}

        logging.info(f"Found {len(files)} files to process")

        # Filter out files that should be skipped
        if self.skip_existing:
            files_to_process = [f for f in files if not self._should_skip_file(f)]
            skipped_count = len(files) - len(files_to_process)
            if skipped_count > 0:
                logging.info(f"Skipping {skipped_count} files that already exist and are up-to-date")
            files = files_to_process

        if not files:
            logging.info("All files are up-to-date, nothing to process.")
            return {"total_files": len(files), "processed": 0, "enhanced": 0, "failed": 0}

        # Process files with progress bar
        results = []

        # Use single-threaded processing for AI enhancement to avoid rate limits
        # For non-AI processing, we could use multiple threads
        if self.ai_enabled and self.ai_enhancer:
            # Single-threaded for AI processing
            with tqdm(files, desc="Processing files", unit="file") as pbar:
                for file_path in pbar:
                    pbar.set_description(f"Processing {file_path.name}")
                    result = self.process_file(file_path)
                    results.append(result)

                    # Update progress bar
                    status = "✅" if result.success else "❌"
                    ai_status = "🤖" if result.enhanced else ""
                    skip_status = "⏭️" if result.success and not result.enhanced and self.skip_existing else ""
                    pbar.set_postfix_str(f"{status} {ai_status} {skip_status}")
        else:
            # Multi-threaded for non-AI processing
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                with tqdm(total=len(files), desc="Processing files", unit="file") as pbar:
                    # Submit all tasks
                    future_to_file = {executor.submit(self.process_file, file_path): file_path
                                    for file_path in files}

                    # Collect results as they complete
                    for future in concurrent.futures.as_completed(future_to_file):
                        file_path = future_to_file[future]
                        try:
                            result = future.result()
                            results.append(result)

                            # Update progress bar
                            status = "✅" if result.success else "❌"
                            pbar.set_postfix_str(f"{status}")
                            pbar.update(1)
                        except Exception as e:
                            logging.error(f"Unexpected error processing {file_path}: {e}")
                            results.append(ProcessingResult(
                                file_path=file_path,
                                success=False,
                                enhanced=False,
                                error_message=str(e)
                            ))
                            pbar.update(1)

        # Generate summary
        summary = self._generate_summary(results)
        self._print_summary(summary)

        return summary
    
    def _generate_summary(self, results: List[ProcessingResult]) -> Dict[str, any]:
        """Generate processing summary statistics."""
        total_files = len(results)
        processed = sum(1 for r in results if r.success)
        enhanced = sum(1 for r in results if r.enhanced)
        failed = sum(1 for r in results if not r.success)
        
        total_time = sum(r.processing_time for r in results)
        avg_time = total_time / total_files if total_files > 0 else 0
        
        return {
            "total_files": total_files,
            "processed": processed,
            "enhanced": enhanced,
            "failed": failed,
            "total_time": total_time,
            "average_time": avg_time,
            "success_rate": (processed / total_files * 100) if total_files > 0 else 0,
            "enhancement_rate": (enhanced / processed * 100) if processed > 0 else 0,
            "results": results
        }
    
    def _print_summary(self, summary: Dict[str, any]) -> None:
        """Print processing summary to console and log."""
        logging.info("\n" + "="*60)
        logging.info("📊 PROCESSING SUMMARY")
        logging.info("="*60)
        logging.info(f"Total files found: {summary['total_files']}")
        logging.info(f"Successfully processed: {summary['processed']}")
        logging.info(f"AI enhanced: {summary['enhanced']}")
        logging.info(f"Failed: {summary['failed']}")
        logging.info(f"Success rate: {summary['success_rate']:.1f}%")
        logging.info(f"Enhancement rate: {summary['enhancement_rate']:.1f}%")
        logging.info(f"Total processing time: {summary['total_time']:.2f}s")
        logging.info(f"Average time per file: {summary['average_time']:.2f}s")
        logging.info(f"Output directory: {self.output_dir}")
        
        if summary['failed'] > 0:
            logging.info("\n❌ Failed files:")
            for result in summary['results']:
                if not result.success:
                    logging.info(f"  - {result.file_path.name}: {result.error_message}")


def main():
    """Main function to run the text enhancer."""
    parser = argparse.ArgumentParser(
        description="Enhance writing style and quality of text files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python text_enhancer.py input.md
  python text_enhancer.py input_folder/ -o enhanced_output/
  python text_enhancer.py input_folder/ --no-ai --suffix _improved
        """
    )
    
    parser.add_argument("input", help="Input file or directory path")
    parser.add_argument("-o", "--output", default="enhanced_output",
                       help="Output directory (default: enhanced_output)")
    parser.add_argument("--no-ai", action="store_true",
                       help="Disable AI enhancement")
    parser.add_argument("--suffix", default="_enhanced",
                       help="Suffix for enhanced filenames (default: _enhanced)")
    parser.add_argument("--flat", action="store_true",
                       help="Don't preserve directory structure")
    parser.add_argument("-v", "--verbose", action="store_true",
                       help="Enable verbose logging")
    parser.add_argument("--max-workers", type=int, default=1,
                       help="Maximum number of concurrent workers (default: 1 for AI processing)")
    parser.add_argument("--force", action="store_true",
                       help="Force reprocessing of existing files")
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Initialize enhancer
        enhancer = TextEnhancer(
            input_path=args.input,
            output_dir=args.output,
            ai_enabled=not args.no_ai,
            preserve_structure=not args.flat,
            suffix=args.suffix,
            max_workers=args.max_workers,
            skip_existing=not args.force
        )
        
        # Process files
        summary = enhancer.process_all()
        
        # Exit with appropriate code
        sys.exit(0 if summary['failed'] == 0 else 1)
        
    except Exception as e:
        logging.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
