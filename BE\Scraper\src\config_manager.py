"""
Configuration Manager for MetruyenScraper
Handles loading and validation of configuration files
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class ConfigManager:
    """Manages configuration loading and validation"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_target_config(self, target_name: str) -> Dict[str, Any]:
        """Get configuration for specific target website"""
        targets = self.get('targets', {})
        if target_name not in targets:
            raise ValueError(f"Target '{target_name}' not found in configuration")
        
        return targets[target_name]
    
    def get_scraper_config(self) -> Dict[str, Any]:
        """Get scraper configuration"""
        return self.get('scraper', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """Get output configuration"""
        return self.get('output', {})
    
    def update_config(self, key: str, value: Any) -> None:
        """Update configuration value"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        logger.info(f"Configuration updated: {key} = {value}")
    
    def save_config(self, path: Optional[str] = None) -> None:
        """Save current configuration to file"""
        save_path = Path(path) if path else self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            raise
    
    def get_selectors(self, target_name: str) -> Dict[str, str]:
        """Get CSS selectors for target website"""
        target_config = self.get_target_config(target_name)
        return target_config.get('selectors', {})
    
    def get_user_agents(self) -> list:
        """Get list of user agents for rotation"""
        return self.get('scraper.stealth.user_agents', [])
    
    def get_delays(self) -> Dict[str, int]:
        """Get delay configuration"""
        return self.get('scraper.stealth.delays', {})
    
    def get_retry_config(self) -> Dict[str, int]:
        """Get retry configuration"""
        return self.get('scraper.retry', {})
