"""
Job Management API Endpoints

This module provides endpoints for tracking and managing
scraping and enhancement job status and progress.
"""

from typing import List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Path, Query
from fastapi.responses import J<PERSON>NResponse

from API.models.schemas import JobStatusResponse, APIResponse
from API.models.database import ScrapingStatus
from API.utils.database import scraping_job_service, enhancement_job_service
from API.middleware.error_handling import DatabaseError, JobError
from API.utils.logging_config import LoggerMixin

router = APIRouter()


class JobController(LoggerMixin):
    """Controller for job management operations"""
    
    async def get_job_status(self, job_id: str, job_type: str = None) -> JobStatusResponse:
        """Get status of a specific job"""
        try:
            self.log_info(f"Getting job status for: {job_id}")
            
            # Try to find job in both collections if type not specified
            job = None
            actual_job_type = job_type
            
            if job_type == "scraping" or job_type is None:
                job = await scraping_job_service.find_by_id(job_id)
                if job:
                    actual_job_type = "scraping"
            
            if not job and (job_type == "enhancement" or job_type is None):
                job = await enhancement_job_service.find_by_id(job_id)
                if job:
                    actual_job_type = "enhancement"
            
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")
            
            # Calculate estimated completion time
            estimated_completion = None
            if job["status"] == "in_progress" and job.get("started_at"):
                elapsed_time = datetime.utcnow() - job["started_at"]
                if job["completed_items"] > 0:
                    avg_time_per_item = elapsed_time.total_seconds() / job["completed_items"]
                    remaining_items = job["total_items"] - job["completed_items"]
                    estimated_seconds = remaining_items * avg_time_per_item
                    estimated_completion = datetime.utcnow() + timedelta(seconds=estimated_seconds)
            
            return JobStatusResponse(
                success=True,
                message="Job status retrieved successfully",
                job_id=str(job["_id"]),
                job_type=actual_job_type,
                status=ScrapingStatus(job["status"]),
                progress_percentage=job["progress_percentage"],
                total_items=job.get("total_items"),
                completed_items=job["completed_items"],
                failed_items=job["failed_items"],
                errors=job.get("errors", []),
                created_at=job["created_at"],
                started_at=job.get("started_at"),
                completed_at=job.get("completed_at"),
                estimated_completion=estimated_completion
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error getting job status: {e}")
            raise DatabaseError(f"Failed to retrieve job status: {e}")
    
    async def get_active_jobs(self, job_type: str = None) -> List[JobStatusResponse]:
        """Get list of active jobs"""
        try:
            self.log_info(f"Getting active jobs of type: {job_type or 'all'}")
            
            active_jobs = []
            
            # Get active scraping jobs
            if job_type == "scraping" or job_type is None:
                scraping_jobs = await scraping_job_service.find_many(
                    filter_dict={"status": {"$in": ["pending", "in_progress"]}},
                    sort=[("created_at", -1)],
                    limit=50
                )
                
                for job in scraping_jobs:
                    job_status = await self.get_job_status(str(job["_id"]), "scraping")
                    active_jobs.append(job_status)
            
            # Get active enhancement jobs
            if job_type == "enhancement" or job_type is None:
                enhancement_jobs = await enhancement_job_service.find_many(
                    filter_dict={"status": {"$in": ["pending", "in_progress"]}},
                    sort=[("created_at", -1)],
                    limit=50
                )
                
                for job in enhancement_jobs:
                    job_status = await self.get_job_status(str(job["_id"]), "enhancement")
                    active_jobs.append(job_status)
            
            # Sort by creation time
            active_jobs.sort(key=lambda x: x.created_at, reverse=True)
            
            return active_jobs
            
        except Exception as e:
            self.log_error(f"Error getting active jobs: {e}")
            raise DatabaseError(f"Failed to retrieve active jobs: {e}")
    
    async def cancel_job(self, job_id: str, job_type: str = None) -> APIResponse:
        """Cancel a running job"""
        try:
            self.log_info(f"Cancelling job: {job_id}")
            
            # Find the job
            job = None
            service = None
            
            if job_type == "scraping" or job_type is None:
                job = await scraping_job_service.find_by_id(job_id)
                if job:
                    service = scraping_job_service
            
            if not job and (job_type == "enhancement" or job_type is None):
                job = await enhancement_job_service.find_by_id(job_id)
                if job:
                    service = enhancement_job_service
            
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")
            
            # Check if job can be cancelled
            if job["status"] in ["completed", "failed", "cancelled"]:
                return APIResponse(
                    success=False,
                    message=f"Cannot cancel job with status: {job['status']}"
                )
            
            # Update job status to cancelled
            await service.update_job_progress(
                job_id,
                completed_items=job["completed_items"],
                status="cancelled"
            )
            
            return APIResponse(
                success=True,
                message="Job cancelled successfully"
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error cancelling job: {e}")
            raise JobError(f"Failed to cancel job: {e}", job_id=job_id)
    
    async def cleanup_old_jobs(self, retention_days: int = 7) -> APIResponse:
        """Clean up old completed jobs"""
        try:
            self.log_info(f"Cleaning up jobs older than {retention_days} days")
            
            # Cleanup scraping jobs
            scraping_count = await scraping_job_service.cleanup_old_jobs(retention_days)
            
            # Cleanup enhancement jobs
            enhancement_count = await enhancement_job_service.cleanup_old_jobs(retention_days)
            
            total_cleaned = scraping_count + enhancement_count
            
            return APIResponse(
                success=True,
                message=f"Cleaned up {total_cleaned} old jobs ({scraping_count} scraping, {enhancement_count} enhancement)"
            )
            
        except Exception as e:
            self.log_error(f"Error cleaning up old jobs: {e}")
            raise JobError(f"Failed to cleanup old jobs: {e}")
    
    async def get_job_statistics(self) -> dict:
        """Get job statistics"""
        try:
            self.log_info("Getting job statistics")
            
            # Get scraping job stats
            scraping_stats = await scraping_job_service.aggregate([
                {
                    "$group": {
                        "_id": "$status",
                        "count": {"$sum": 1}
                    }
                }
            ])
            
            # Get enhancement job stats
            enhancement_stats = await enhancement_job_service.aggregate([
                {
                    "$group": {
                        "_id": "$status",
                        "count": {"$sum": 1}
                    }
                }
            ])
            
            # Process stats
            scraping_by_status = {stat["_id"]: stat["count"] for stat in scraping_stats}
            enhancement_by_status = {stat["_id"]: stat["count"] for stat in enhancement_stats}
            
            return {
                "scraping_jobs": {
                    "total": sum(scraping_by_status.values()),
                    "by_status": scraping_by_status
                },
                "enhancement_jobs": {
                    "total": sum(enhancement_by_status.values()),
                    "by_status": enhancement_by_status
                },
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Error getting job statistics: {e}")
            raise DatabaseError(f"Failed to retrieve job statistics: {e}")


# ============================================================================
# Router Endpoints
# ============================================================================

controller = JobController()


@router.get("/{job_id}/status", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str = Path(..., description="Job ID"),
    job_type: Optional[str] = Query(None, pattern="^(scraping|enhancement)$", description="Job type")
):
    """
    Get status and progress of a specific job
    
    This endpoint returns detailed information about a job including
    progress, errors, and estimated completion time.
    """
    return await controller.get_job_status(job_id, job_type)


@router.get("/active", response_model=List[JobStatusResponse])
async def get_active_jobs(
    job_type: Optional[str] = Query(None, pattern="^(scraping|enhancement)$", description="Filter by job type")
):
    """
    Get list of all active jobs
    
    This endpoint returns all currently running or pending jobs
    with their status and progress information.
    """
    return await controller.get_active_jobs(job_type)


@router.post("/{job_id}/cancel", response_model=APIResponse)
async def cancel_job(
    job_id: str = Path(..., description="Job ID"),
    job_type: Optional[str] = Query(None, pattern="^(scraping|enhancement)$", description="Job type")
):
    """
    Cancel a running job
    
    This endpoint cancels a job that is currently pending or in progress.
    Completed or already cancelled jobs cannot be cancelled.
    """
    return await controller.cancel_job(job_id, job_type)


@router.post("/cleanup", response_model=APIResponse)
async def cleanup_old_jobs(
    retention_days: int = Query(7, ge=1, le=365, description="Days to retain completed jobs")
):
    """
    Clean up old completed jobs
    
    This endpoint removes old completed, failed, or cancelled jobs
    to free up database space.
    """
    return await controller.cleanup_old_jobs(retention_days)


@router.get("/statistics")
async def get_job_statistics():
    """
    Get job statistics
    
    This endpoint returns statistics about jobs including counts
    by status and type.
    """
    return await controller.get_job_statistics()
