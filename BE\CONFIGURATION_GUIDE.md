# Configuration Management Guide

## Overview

This document explains the unified configuration structure for the Web Novel Backend Service after cleanup and reorganization.

## Configuration Files Structure

### Environment Variables
- **Location**: `API/.env`
- **Template**: `API/.env.template`
- **Purpose**: Contains sensitive configuration like API keys, database URLs, and environment-specific settings
- **Usage**: Used by the main API application and all services

### Scraper Configuration
- **Location**: `Scraper/config.yaml`
- **Purpose**: Contains scraper-specific settings like browser options, target selectors, retry mechanisms
- **Usage**: Used by both standalone scraper and API when performing scraping operations

## Setup Instructions

### 1. Environment Configuration

```bash
# Copy the template file
cp API/.env.template API/.env

# Edit the .env file with your actual values
# Required variables:
# - MONGODB_URL
# - GOOGLE_AI_API_KEY
```

### 2. Scraper Configuration

The `Scraper/config.yaml` file is ready to use with default settings. You can modify:
- Browser settings (headless mode, timeout, viewport)
- Target website selectors
- Retry and stealth configurations
- Logging preferences

## Changes Made

### Removed Files
- `BE/.env.template` (duplicate, kept only in API/)
- `BE/config.yaml` (duplicate, kept only in Scraper/)
- `BE/requirements.txt` (each module has its own)
- `AITextEnhancer/.gitignore`, `API/.gitignore`, `Scraper/.gitignore` (consolidated into root .gitignore)

### Updated References
- API now references `Scraper/config.yaml` instead of `BE/config.yaml`
- Documentation updated to reflect new structure
- All paths in code updated accordingly

## Benefits

1. **No Duplication**: Single source of truth for each configuration type
2. **Clear Separation**: Environment variables vs application settings
3. **Module Isolation**: Each module owns its specific configuration
4. **Reduced Confusion**: No conflicting or duplicate configuration files
5. **Better Maintainability**: Easier to manage and update configurations

## Best Practices

1. **Never commit `.env` files**: They contain sensitive information
2. **Use `.env.template`**: As a reference for required variables
3. **Module-specific configs**: Keep configuration close to where it's used
4. **Document changes**: Update this guide when adding new configuration options

## Troubleshooting

### Common Issues

1. **Missing .env file**: Copy from template and fill in required values
2. **Config not found**: Ensure `Scraper/config.yaml` exists and is accessible
3. **Permission errors**: Check file permissions on configuration files

### Validation

To verify configuration is working:

```bash
# Test API startup
cd API
python main.py

# Should see:
# ✅ Scraper configured
# ✅ Enhancement service initialized
# ✅ Application startup completed
```

## Migration Notes

If you had existing configuration files:

1. **Old `BE/.env`**: Move contents to `API/.env`
2. **Old `BE/config.yaml`**: Content is now in `Scraper/config.yaml`
3. **Module-specific .env files**: Consolidate into `API/.env`

The system will automatically use the new configuration structure.