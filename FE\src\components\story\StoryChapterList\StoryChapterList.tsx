'use client';

import React, { useState, useEffect } from 'react';
import { Chapter } from '@/types/story';
import { useChapterManagement } from '@/hooks/useChapterManagement/useChapterManagement';
import { useBatchActions } from '@/hooks/useBatchActions/useBatchActions';
import { ChapterListHeader } from './ChapterListHeader';
import { BatchActionsPanel } from './BatchActionsPanel';
import { ChapterList } from './ChapterList';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { Pagination } from '@/components/ui/pagination';

interface StoryChapterListProps {
  storyId: string;
}

const StoryChapterList: React.FC<StoryChapterListProps> = ({ storyId }) => {
  const [selectionMode, setSelectionMode] = useState<'scraping' | 'enhancement' | null>(null);
  const { toasts, removeToast } = useToast();

  const {
    chapters,
    chaptersLoading,
    filter,
    setFilter,
    currentPage,
    handlePageChange,
    handlePageSizeChange,
    totalPages,
    chapterStats,
    selectedChapters,
    pageSize,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
  } = useChapterManagement(storyId);

  const { 
    batchScrapingState, 
    batchEnhancementState, 
    handleBatchScrape, 
    handleBatchEnhance, 
    cancelAction
  } = useBatchActions(() => {
    setSelectionMode(null);
    handleDeselectAll();
  });

  const handleStartAction = () => {
    const chapterIds = Array.from(selectedChapters);
    if (selectionMode === 'scraping') {
      handleBatchScrape(storyId, chapterIds);
    } else if (selectionMode === 'enhancement') {
      handleBatchEnhance(storyId, chapterIds);
    }
  };

  const handleCancelAction = () => {
    const jobId = selectionMode === 'scraping' ? batchScrapingState.jobId : batchEnhancementState.jobId;
    if (jobId) {
      cancelAction(jobId);
    } else {
      setSelectionMode(null);
      handleDeselectAll();
    }
  };

  useEffect(() => {
    handleDeselectAll();
  }, [selectionMode, handleDeselectAll]);

  const isProcessing = batchScrapingState.isActive || batchEnhancementState.isActive;
  const currentProgress = selectionMode === 'scraping' 
    ? batchScrapingState.progress
    : batchEnhancementState.progress;

  const progressPercentage = currentProgress?.progress_percentage ?? 0;

  return (
    <div className="space-y-4">
      <ToastContainer toasts={toasts} onDismiss={removeToast} />
      <ChapterListHeader
        stats={chapterStats}
        selectionMode={selectionMode}
        setSelectionMode={setSelectionMode}
        filter={filter}
        onFilterChange={setFilter}
      />

      {selectionMode && (
        <BatchActionsPanel
          selectionMode={selectionMode}
          isProcessing={isProcessing}
          progress={progressPercentage}
          onStartAction={handleStartAction}
          onCancel={handleCancelAction}
          selectedCount={selectedChapters.size}
        />
      )}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={chapterStats.total}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        showPageSizeSelector={true}
        showJumpToPage={false}
        showItemsInfo={false}
      />

      <ChapterList
        storyId={storyId}
        chapters={chapters}
        loading={chaptersLoading}
        selectionMode={selectionMode}
        selectedChapters={selectedChapters}
        onSelectChapter={handleSelectChapter}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />
    </div>
  );
};

export default StoryChapterList;