/**
 * Hierarchical Scraping Service
 * 
 * Provides comprehensive API integration for the hierarchical web scraping system.
 * Implements the Story → Page → Chapter workflow with proper error handling,
 * progress tracking, and rate limiting awareness.
 */

import {
  HierarchicalScrapeRequest,
  HierarchicalScrapeResponse,
  StoryHierarchy,
  PageInfo,
  ChapterInfo,
  ChapterContent,
  ScrapingStats,
  BatchScrapingResult,
  ScrapingProgress
} from '@/types/scrape';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
const API_PREFIX = '/api/v1/hierarchical';

// ============================================================================
// Error Handling
// ============================================================================

export class HierarchicalScrapingError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'HierarchicalScrapingError';
  }
}

const handleApiError = async (response: Response): Promise<never> => {
  let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
  let details: any = null;

  try {
    const errorData = await response.json();
    errorMessage = errorData.message || errorData.detail || errorMessage;
    details = errorData;
  } catch {
    // If we can't parse the error response, use the default message
  }

  throw new HierarchicalScrapingError(errorMessage, response.status, details);
};

const makeApiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const url = `${API_BASE_URL}${API_PREFIX}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  try {
    const response = await fetch(url, {
      ...options,
      headers: defaultHeaders,
    });

    if (!response.ok) {
      await handleApiError(response);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof HierarchicalScrapingError) {
      throw error;
    }
    
    // Network or other errors
    throw new HierarchicalScrapingError(
      `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      0,
      error
    );
  }
};

// ============================================================================
// Core Scraping Operations
// ============================================================================

/**
 * Perform complete hierarchical scraping of a story
 * Phase 1-3: Story Info → Page Processing → Chapter URL Extraction
 */
export const scrapeStoryHierarchy = async (
  request: HierarchicalScrapeRequest
): Promise<HierarchicalScrapeResponse> => {
  return makeApiRequest<HierarchicalScrapeResponse>('/scrape', {
    method: 'POST',
    body: JSON.stringify(request),
  });
};

/**
 * Get complete story hierarchy including pages and chapters
 */
export const getStoryHierarchy = async (
  storyId: string,
  includeChapters: boolean = true
): Promise<StoryHierarchy> => {
  const params = new URLSearchParams();
  if (!includeChapters) {
    params.append('include_chapters', 'false');
  }
  
  const endpoint = `/story/${storyId}${params.toString() ? `?${params.toString()}` : ''}`;
  return makeApiRequest<StoryHierarchy>(endpoint);
};

/**
 * Get pages for a specific story
 */
export const getStoryPages = async (
  storyId: string,
  pageNumber?: number
): Promise<{ story_id: string; total_pages: number; pages: PageInfo[] }> => {
  const params = new URLSearchParams();
  if (pageNumber !== undefined) {
    params.append('page_number', pageNumber.toString());
  }
  
  const endpoint = `/story/${storyId}/pages${params.toString() ? `?${params.toString()}` : ''}`;
  return makeApiRequest(endpoint);
};

/**
 * Get all chapters for a specific page
 */
export const getPageChapters = async (pageId: string): Promise<{
  page_id: string;
  page_number: number;
  page_url: string;
  total_chapters_on_page: number;
  chapters: ChapterInfo[];
}> => {
  return makeApiRequest(`/page/${pageId}/chapters`);
};

// ============================================================================
// Chapter Content Operations
// ============================================================================

/**
 * Scrape content for a specific chapter (Phase 4)
 */
export const scrapeChapterContent = async (
  chapterId: string
): Promise<ChapterContent> => {
  return makeApiRequest<ChapterContent>(`/chapter/${chapterId}/content`, {
    method: 'POST',
  });
};

/**
 * Scrape content for a specific chapter by URL
 */
export const scrapeChapterByUrl = async (
  chapterUrl: string
): Promise<ChapterContent> => {
  // First, we need to find the chapter by URL or create a scraping request
  // This would require additional backend endpoint or modification
  throw new Error('Chapter scraping by URL not yet implemented');
};

/**
 * Scrape content for all chapters in a story with batch processing
 */
export const scrapeAllStoryChapters = async (
  storyId: string,
  maxConcurrent: number = 3,
  onProgress?: (progress: { scraped: number; total: number; current?: string }) => void
): Promise<BatchScrapingResult> => {
  const params = new URLSearchParams();
  params.append('max_concurrent', maxConcurrent.toString());
  
  // For now, we'll use the simple batch endpoint
  // In the future, we could implement streaming progress updates
  const result = await makeApiRequest<BatchScrapingResult>(
    `/story/${storyId}/scrape-all-chapters?${params.toString()}`,
    { method: 'POST' }
  );
  
  // Call progress callback with final result
  if (onProgress) {
    onProgress({
      scraped: result.scraped_count,
      total: result.scraped_count + result.failed_count
    });
  }
  
  return result;
};

// ============================================================================
// Statistics and Monitoring
// ============================================================================

/**
 * Get hierarchical scraping system statistics
 */
export const getScrapingStats = async (): Promise<ScrapingStats> => {
  return makeApiRequest<ScrapingStats>('/stats');
};

// ============================================================================
// Progress Tracking Utilities
// ============================================================================

/**
 * Create a progress tracker for hierarchical scraping operations
 */
export const createProgressTracker = () => {
  const listeners: ((progress: ScrapingProgress) => void)[] = [];
  
  const updateProgress = (progress: ScrapingProgress) => {
    listeners.forEach(listener => listener(progress));
  };
  
  const addListener = (listener: (progress: ScrapingProgress) => void) => {
    listeners.push(listener);
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  };
  
  return { updateProgress, addListener };
};

/**
 * Execute hierarchical scraping with progress tracking
 */
export const scrapeWithProgress = async (
  request: HierarchicalScrapeRequest,
  onProgress?: (progress: ScrapingProgress) => void
): Promise<HierarchicalScrapeResponse> => {
  const updateProgress = (phase: ScrapingProgress['phase'], step: number, total: number, message: string, data?: any) => {
    if (onProgress) {
      onProgress({ phase, current_step: step, total_steps: total, message, data });
    }
  };
  
  try {
    updateProgress('story_info', 1, 4, 'Starting story information extraction...');
    
    // Execute the scraping operation
    const result = await scrapeStoryHierarchy(request);
    
    updateProgress('pages', 2, 4, `Processing ${result.total_pages} pages...`);
    updateProgress('chapters', 3, 4, `Extracting ${result.total_chapters} chapter URLs...`);
    
    if (request.scrape_content) {
      updateProgress('content', 4, 4, 'Scraping chapter content...');
    }
    
    updateProgress('completed', 4, 4, 'Hierarchical scraping completed successfully!', result);
    
    return result;
  } catch (error) {
    updateProgress('error', 0, 4, error instanceof Error ? error.message : 'Unknown error occurred');
    throw error;
  }
};

// ============================================================================
// Rate Limiting Utilities
// ============================================================================

/**
 * Simple rate limiter for client-side operations
 */
export class RateLimiter {
  private requests: number[] = [];
  
  constructor(
    private maxRequests: number = 10,
    private windowMs: number = 60000 // 1 minute
  ) {}
  
  async waitIfNeeded(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    
    this.requests.push(now);
  }
}

// ============================================================================
// Validation Utilities
// ============================================================================

/**
 * Validate story URL format
 */
export const validateStoryUrl = (url: string): { valid: boolean; message?: string } => {
  if (!url || !url.trim()) {
    return { valid: false, message: 'URL is required' };
  }
  
  try {
    const urlObj = new URL(url);
    
    // Basic URL validation
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { valid: false, message: 'URL must use HTTP or HTTPS protocol' };
    }
    
    // You can add more specific validation for supported domains here
    
    return { valid: true };
  } catch {
    return { valid: false, message: 'Invalid URL format' };
  }
};

/**
 * Validate scraping request parameters
 */
export const validateScrapeRequest = (request: HierarchicalScrapeRequest): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Validate URL
  const urlValidation = validateStoryUrl(request.story_url);
  if (!urlValidation.valid) {
    errors.push(urlValidation.message || 'Invalid URL');
  }
  
  // Validate max_pages
  if (request.max_pages !== undefined) {
    if (request.max_pages < 1 || request.max_pages > 100) {
      errors.push('Max pages must be between 1 and 100');
    }
  }
  
  return { valid: errors.length === 0, errors };
};

// ============================================================================
// Export all functions
// ============================================================================

export default {
  // Core operations
  scrapeStoryHierarchy,
  getStoryHierarchy,
  getStoryPages,
  getPageChapters,
  scrapeChapterContent,
  scrapeAllStoryChapters,
  
  // Statistics
  getScrapingStats,
  
  // Progress tracking
  createProgressTracker,
  scrapeWithProgress,
  
  // Utilities
  RateLimiter,
  validateStoryUrl,
  validateScrapeRequest,
  
  // Error class
  HierarchicalScrapingError
};