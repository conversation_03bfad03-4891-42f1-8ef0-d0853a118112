'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Sparkles, SlidersHorizontal, X } from 'lucide-react';
import ChapterStatusFilter from '@/components/story/ChapterStatusFilter';
import { ChapterFilter } from '@/hooks/useChapterManagement';

interface ChapterListHeaderProps {
  stats: {
    total: number;
    scraped: number;
    enhanced: number;
    unscraped: number;
    scrapedNotEnhanced: number;
    selected: number;
  };
  selectionMode: 'scraping' | 'enhancement' | null;
  setSelectionMode: (mode: 'scraping' | 'enhancement' | null) => void;
  onFilterChange: (filter: ChapterFilter) => void;
  filter: ChapterFilter;
}

export const ChapterListHeader: React.FC<ChapterListHeaderProps> = ({ 
  stats, 
  selectionMode, 
  setSelectionMode,
  onFilterChange,
  filter
}) => {
  const handleToggleScrapeMode = () => {
    setSelectionMode(selectionMode === 'scraping' ? null : 'scraping');
  };

  const handleToggleEnhanceMode = () => {
    setSelectionMode(selectionMode === 'enhancement' ? null : 'enhancement');
  };

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700/50">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center mb-4">
        <div><p className="text-sm text-gray-500">Total</p><p className="font-bold text-lg">{stats.total}</p></div>
        <div><p className="text-sm text-gray-500">Scraped</p><p className="font-bold text-lg text-green-500">{stats.scraped}</p></div>
        <div><p className="text-sm text-gray-500">Enhanced</p><p className="font-bold text-lg text-blue-500">{stats.enhanced}</p></div>
        <div><p className="text-sm text-gray-500">Selected</p><p className="font-bold text-lg text-indigo-500">{stats.selected}</p></div>
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Button 
            variant={selectionMode === 'scraping' ? 'default' : 'outline'}
            onClick={handleToggleScrapeMode}
            className="gap-2"
          >
            {selectionMode === 'scraping' ? <X className="h-4 w-4" /> : <Download className="h-4 w-4" />}
            Scrape
          </Button>
          <Button 
            variant={selectionMode === 'enhancement' ? 'default' : 'outline'}
            onClick={handleToggleEnhanceMode}
            className="gap-2"
          >
            {selectionMode === 'enhancement' ? <X className="h-4 w-4" /> : <Sparkles className="h-4 w-4" />}
            Enhance
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <SlidersHorizontal className="h-5 w-5 text-gray-500" />
          <ChapterStatusFilter 
            filter={filter} 
            onFilterChange={onFilterChange} 
          />
        </div>
      </div>
    </div>
  );
};
