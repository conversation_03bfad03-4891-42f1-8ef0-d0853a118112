'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  HierarchicalScrapeRequest,
  HierarchicalScrapeResponse,
  StoryHierarchy,
  ScrapingProgress,
  ScrapingStats,
  BatchScrapingResult
} from '@/types/scrape';
import {
  scrapeWithProgress,
  getStoryHierarchy,
  getScrapingStats,
  scrapeAllStoryChapters,
  validateScrapeRequest,
  HierarchicalScrapingError
} from '@/services/hierarchicalScrapeService';
import { clsx } from 'clsx';

// ============================================================================
// Types
// ============================================================================

interface DashboardState {
  currentView: 'scrape' | 'hierarchy' | 'stats';
  isLoading: boolean;
  error: string | null;
  success: string | null;
}

interface ScrapeFormData {
  story_url: string;
  max_pages: number;
  scrape_content: boolean;
}

// ============================================================================
// Main Dashboard Component
// ============================================================================

const HierarchicalScrapingDashboard: React.FC = () => {
  // State management
  const [state, setState] = useState<DashboardState>({
    currentView: 'scrape',
    isLoading: false,
    error: null,
    success: null
  });

  const [formData, setFormData] = useState<ScrapeFormData>({
    story_url: '',
    max_pages: 10,
    scrape_content: false
  });

  const [progress, setProgress] = useState<ScrapingProgress | null>(null);
  const [lastResult, setLastResult] = useState<HierarchicalScrapeResponse | null>(null);
  const [storyHierarchy, setStoryHierarchy] = useState<StoryHierarchy | null>(null);
  const [stats, setStats] = useState<ScrapingStats | null>(null);

  // ========================================================================
  // Utility Functions
  // ========================================================================

  const updateState = useCallback((updates: Partial<DashboardState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const clearMessages = useCallback(() => {
    updateState({ error: null, success: null });
  }, [updateState]);

  const showError = useCallback((error: string) => {
    updateState({ error, success: null, isLoading: false });
  }, [updateState]);

  const showSuccess = useCallback((message: string) => {
    updateState({ success: message, error: null });
  }, [updateState]);

  // ========================================================================
  // API Operations
  // ========================================================================

  const handleScrapeStory = async () => {
    clearMessages();
    
    // Validate form data
    const validation = validateScrapeRequest(formData);
    if (!validation.valid) {
      showError(validation.errors.join(', '));
      return;
    }

    updateState({ isLoading: true });
    setProgress(null);
    setLastResult(null);

    try {
      const result = await scrapeWithProgress(
        formData,
        (progressUpdate) => {
          setProgress(progressUpdate);
        }
      );

      setLastResult(result);
      showSuccess(`Successfully scraped "${result.title}" with ${result.total_chapters} chapters across ${result.total_pages} pages`);
      
      // Load the hierarchy for the newly scraped story
      await loadStoryHierarchy(result.story_id);
      
    } catch (error) {
      const errorMessage = error instanceof HierarchicalScrapingError 
        ? error.message 
        : 'An unexpected error occurred';
      showError(errorMessage);
      setProgress(null);
    } finally {
      updateState({ isLoading: false });
    }
  };

  const loadStoryHierarchy = async (storyId: string) => {
    try {
      const hierarchy = await getStoryHierarchy(storyId, true);
      setStoryHierarchy(hierarchy);
      updateState({ currentView: 'hierarchy' });
    } catch (error) {
      console.error('Failed to load story hierarchy:', error);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getScrapingStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleScrapeAllChapters = async () => {
    if (!storyHierarchy) return;

    updateState({ isLoading: true });
    clearMessages();

    try {
      const result = await scrapeAllStoryChapters(
        storyHierarchy.story_id,
        3, // max concurrent
        (progress) => {
          // Update progress if needed
          console.log('Chapter scraping progress:', progress);
        }
      );

      showSuccess(`Scraped content for ${result.scraped_count} chapters. ${result.failed_count} failed.`);
      
      // Reload hierarchy to show updated scraping status
      await loadStoryHierarchy(storyHierarchy.story_id);
      
    } catch (error) {
      const errorMessage = error instanceof HierarchicalScrapingError 
        ? error.message 
        : 'Failed to scrape chapter content';
      showError(errorMessage);
    } finally {
      updateState({ isLoading: false });
    }
  };

  // ========================================================================
  // Effects
  // ========================================================================

  useEffect(() => {
    if (state.currentView === 'stats') {
      loadStats();
    }
  }, [state.currentView]);

  // ========================================================================
  // Render Functions
  // ========================================================================

  const renderNavigationTabs = () => (
    <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
      {[
        { key: 'scrape', label: 'Scrape Story', icon: '🚀' },
        { key: 'hierarchy', label: 'Story Hierarchy', icon: '📚' },
        { key: 'stats', label: 'Statistics', icon: '📊' }
      ].map(tab => (
        <button
          key={tab.key}
          onClick={() => updateState({ currentView: tab.key as any })}
          className={clsx(
            'flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors',
            state.currentView === tab.key
              ? 'bg-indigo-600 text-white'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          )}
        >
          <span>{tab.icon}</span>
          <span>{tab.label}</span>
        </button>
      ))}
    </div>
  );

  const renderProgressIndicator = () => {
    if (!progress) return null;

    const progressPercentage = (progress.current_step / progress.total_steps) * 100;
    
    const phaseEmojis = {
      story_info: '📖',
      pages: '📄',
      chapters: '📝',
      content: '💾',
      completed: '✅',
      error: '❌'
    };

    return (
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
            <span>{phaseEmojis[progress.phase]}</span>
            <span>Scraping Progress</span>
          </h3>
          <span className="text-sm text-gray-400">
            Step {progress.current_step} of {progress.total_steps}
          </span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
          <div 
            className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        
        <p className="text-gray-300 text-sm">{progress.message}</p>
        
        {progress.phase === 'completed' && progress.data && (
          <div className="mt-3 p-3 bg-green-900/20 border border-green-500/30 rounded-md">
            <p className="text-green-400 text-sm">
              ✅ Successfully processed {progress.data.total_chapters} chapters across {progress.data.total_pages} pages
            </p>
          </div>
        )}
      </div>
    );
  };

  const renderScrapeForm = () => (
    <div className="bg-gray-800 rounded-lg p-6">
      <h2 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2">
        <span>🚀</span>
        <span>Hierarchical Story Scraping</span>
      </h2>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="story_url" className="block text-sm font-medium text-gray-300 mb-2">
            Story URL *
          </label>
          <input
            id="story_url"
            type="url"
            value={formData.story_url}
            onChange={(e) => setFormData(prev => ({ ...prev, story_url: e.target.value }))}
            placeholder="https://example.com/story/123"
            className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
            disabled={state.isLoading}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="max_pages" className="block text-sm font-medium text-gray-300 mb-2">
              Max Pages to Scrape
            </label>
            <input
              id="max_pages"
              type="number"
              min="1"
              max="100"
              value={formData.max_pages}
              onChange={(e) => setFormData(prev => ({ ...prev, max_pages: parseInt(e.target.value) || 10 }))}
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              disabled={state.isLoading}
            />
          </div>
          
          <div className="flex items-center">
            <label className="flex items-center space-x-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.scrape_content}
                onChange={(e) => setFormData(prev => ({ ...prev, scrape_content: e.target.checked }))}
                className="w-5 h-5 text-indigo-600 bg-gray-700 border-gray-600 rounded focus:ring-indigo-500 focus:ring-2"
                disabled={state.isLoading}
              />
              <span className="text-sm text-gray-300">
                Also scrape chapter content
              </span>
            </label>
          </div>
        </div>
        
        <button
          onClick={handleScrapeStory}
          disabled={state.isLoading || !formData.story_url.trim()}
          className={clsx(
            'w-full py-3 px-6 rounded-md font-medium transition-colors',
            state.isLoading || !formData.story_url.trim()
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-900'
          )}
        >
          {state.isLoading ? (
            <span className="flex items-center justify-center space-x-2">
              <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span>Scraping in Progress...</span>
            </span>
          ) : (
            'Start Hierarchical Scraping'
          )}
        </button>
      </div>
      
      {lastResult && (
        <div className="mt-6 p-4 bg-green-900/20 border border-green-500/30 rounded-md">
          <h3 className="text-green-400 font-medium mb-2">✅ Last Scraping Result</h3>
          <div className="text-sm text-gray-300 space-y-1">
            <p><strong>Title:</strong> {lastResult.title}</p>
            <p><strong>Story ID:</strong> {lastResult.story_id}</p>
            <p><strong>Pages:</strong> {lastResult.total_pages}</p>
            <p><strong>Chapters:</strong> {lastResult.total_chapters}</p>
          </div>
        </div>
      )}
    </div>
  );

  const renderStoryHierarchy = () => {
    if (!storyHierarchy) {
      return (
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <p className="text-gray-400">No story hierarchy loaded. Please scrape a story first.</p>
        </div>
      );
    }

    const totalChapters = storyHierarchy.pages.reduce((sum, page) => sum + page.total_chapters_on_page, 0);
    const scrapedChapters = storyHierarchy.pages.reduce((sum, page) => {
      return sum + (page.chapters?.filter(ch => ch.is_scraped).length || 0);
    }, 0);

    return (
      <div className="space-y-6">
        {/* Story Overview */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">{storyHierarchy.title}</h2>
              <p className="text-gray-400 text-sm">Story ID: {storyHierarchy.story_id}</p>
            </div>
            <button
              onClick={handleScrapeAllChapters}
              disabled={state.isLoading || scrapedChapters === totalChapters}
              className={clsx(
                'px-4 py-2 rounded-md font-medium transition-colors',
                state.isLoading || scrapedChapters === totalChapters
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-green-600 text-white hover:bg-green-700'
              )}
            >
              {scrapedChapters === totalChapters ? 'All Content Scraped' : 'Scrape All Chapter Content'}
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-400">{storyHierarchy.total_pages}</div>
              <div className="text-sm text-gray-400">Total Pages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{totalChapters}</div>
              <div className="text-sm text-gray-400">Total Chapters</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{scrapedChapters}</div>
              <div className="text-sm text-gray-400">Content Scraped</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {totalChapters > 0 ? Math.round((scrapedChapters / totalChapters) * 100) : 0}%
              </div>
              <div className="text-sm text-gray-400">Progress</div>
            </div>
          </div>
        </div>

        {/* Pages List */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Pages & Chapters</h3>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {storyHierarchy.pages.map((page) => (
              <div key={page.page_id} className="border border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-white">
                    Page {page.page_number} ({page.total_chapters_on_page} chapters)
                  </h4>
                  <span className={clsx(
                    'px-2 py-1 rounded-full text-xs font-medium',
                    page.is_scraped
                      ? 'bg-green-900/30 text-green-400 border border-green-500/30'
                      : 'bg-yellow-900/30 text-yellow-400 border border-yellow-500/30'
                  )}>
                    {page.is_scraped ? 'Scraped' : 'Pending'}
                  </span>
                </div>
                
                {page.chapters && page.chapters.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mt-3">
                    {page.chapters.map((chapter) => (
                      <div
                        key={chapter.chapter_id}
                        className={clsx(
                          'p-2 rounded border text-xs',
                          chapter.is_scraped
                            ? 'bg-green-900/20 border-green-500/30 text-green-300'
                            : 'bg-gray-700 border-gray-600 text-gray-300'
                        )}
                      >
                        <div className="font-medium truncate" title={chapter.title}>
                          Ch. {chapter.chapter_number}
                        </div>
                        <div className="text-gray-400 truncate" title={chapter.title}>
                          {chapter.title}
                        </div>
                        {chapter.word_count && (
                          <div className="text-gray-500">
                            {chapter.word_count} words
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderStats = () => {
    if (!stats) {
      return (
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-indigo-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Loading statistics...</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Overview Stats */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2">
            <span>📊</span>
            <span>Scraping Statistics</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-indigo-400 mb-2">{stats.totals.stories}</div>
              <div className="text-gray-400">Total Stories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{stats.totals.pages}</div>
              <div className="text-gray-400">Total Pages</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">{stats.totals.chapters}</div>
              <div className="text-gray-400">Total Chapters</div>
            </div>
          </div>
        </div>

        {/* Progress Stats */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Progress Overview</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-300">Pages Scraped</span>
                <span className="text-gray-400">{stats.progress.page_scraping_rate}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${stats.progress.page_scraping_rate}%` }}
                />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-300">Chapters Scraped</span>
                <span className="text-gray-400">{stats.progress.chapter_scraping_rate}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full"
                  style={{ width: `${stats.progress.chapter_scraping_rate}%` }}
                />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-300">Chapters Enhanced</span>
                <span className="text-gray-400">{stats.progress.enhancement_rate}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full"
                  style={{ width: `${stats.progress.enhancement_rate}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Recent Stories */}
        {stats.recent_stories.length > 0 && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Recent Stories</h3>
            <div className="space-y-3">
              {stats.recent_stories.map((story) => (
                <div key={story.story_id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                  <div>
                    <div className="font-medium text-white truncate">{story.title}</div>
                    <div className="text-sm text-gray-400">
                      {story.total_pages} pages • {new Date(story.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <button
                    onClick={() => loadStoryHierarchy(story.story_id)}
                    className="px-3 py-1 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700 transition-colors"
                  >
                    View
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMessages = () => {
    if (!state.error && !state.success) return null;

    return (
      <div className="mb-6">
        {state.error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-md p-4 mb-4">
            <div className="flex items-start space-x-3">
              <span className="text-red-400 text-xl">❌</span>
              <div>
                <h3 className="text-red-400 font-medium">Error</h3>
                <p className="text-red-300 text-sm mt-1">{state.error}</p>
              </div>
              <button
                onClick={clearMessages}
                className="ml-auto text-red-400 hover:text-red-300"
              >
                ✕
              </button>
            </div>
          </div>
        )}
        
        {state.success && (
          <div className="bg-green-900/20 border border-green-500/30 rounded-md p-4 mb-4">
            <div className="flex items-start space-x-3">
              <span className="text-green-400 text-xl">✅</span>
              <div>
                <h3 className="text-green-400 font-medium">Success</h3>
                <p className="text-green-300 text-sm mt-1">{state.success}</p>
              </div>
              <button
                onClick={clearMessages}
                className="ml-auto text-green-400 hover:text-green-300"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // ========================================================================
  // Main Render
  // ========================================================================

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Hierarchical Web Scraping Dashboard
          </h1>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Comprehensive story scraping with the Story → Page → Chapter workflow.
            Extract metadata, process pages, and scrape chapter content with progress tracking.
          </p>
        </div>

        {/* Navigation */}
        {renderNavigationTabs()}

        {/* Messages */}
        {renderMessages()}

        {/* Progress Indicator */}
        {renderProgressIndicator()}

        {/* Content */}
        <div className="">
          {state.currentView === 'scrape' && renderScrapeForm()}
          {state.currentView === 'hierarchy' && renderStoryHierarchy()}
          {state.currentView === 'stats' && renderStats()}
        </div>
      </div>
    </div>
  );
};

export default HierarchicalScrapingDashboard;