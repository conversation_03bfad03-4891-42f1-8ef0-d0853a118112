"""
AI Enhancement related API Models
"""
from typing import Optional, List
from pydantic import BaseModel, Field, validator

from .common import APIResponse

# ============================================================================
# AI Enhancement API Models
# ============================================================================

class ChapterEnhancementRequest(BaseModel):
    """Request to enhance chapter content"""
    chapter_id: str
    force_re_enhance: bool = False


class BatchEnhancementRequest(BaseModel):
    """Request to enhance multiple chapters"""
    chapter_ids: Optional[List[str]] = None
    story_id: Optional[str] = None  # Enhance all chapters of a story
    force_re_enhance: bool = False
    batch_size: int = Field(default=1, ge=1, le=5)
    
    @validator('story_id')
    def validate_input(cls, v, values):
        chapter_ids = values.get('chapter_ids')
        story_id = v
        
        if not chapter_ids and not story_id:
            raise ValueError('Either chapter_ids or story_id must be provided')
        if chapter_ids and story_id:
            raise ValueError('Provide either chapter_ids or story_id, not both')
        return v


class EnhancementResult(BaseModel):
    """Single chapter enhancement result"""
    chapter_id: str
    original_length: int
    enhanced_length: int
    improvement_notes: List[str] = Field(default_factory=list)
    enhancement_duration: Optional[float] = None
    quality_score: Optional[float] = None


class BatchEnhancementResponse(APIResponse):
    """Response from batch enhancement"""
    job_id: str
    total_chapters: int
    completed_chapters: int = 0
    results: List[EnhancementResult] = Field(default_factory=list)
    failed_chapters: List[str] = Field(default_factory=list)