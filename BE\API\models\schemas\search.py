"""
Search and Filter related API Models
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field, validator

from .common import APIResponse

# ============================================================================
# Search and Filter API Models
# ============================================================================

class SearchRequest(BaseModel):
    """Advanced search request"""
    query: str = Field(min_length=1)
    search_in: List[str] = Field(default=["title", "author", "description"])
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    filters: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('search_in')
    def validate_search_fields(cls, v):
        valid_fields = ["title", "author", "description", "content"]
        for field in v:
            if field not in valid_fields:
                raise ValueError(f'Invalid search field: {field}')
        return v


class ContentComparisonRequest(BaseModel):
    """Request to compare original and enhanced content"""
    chapter_id: str
    comparison_type: str = Field(default="side_by_side", pattern="^(side_by_side|diff|statistics)$")


class ContentComparisonResponse(APIResponse):
    """Response for content comparison"""
    chapter_id: str
    chapter_title: str
    original_length: int
    enhanced_length: int
    similarity_score: Optional[float] = None
    improvement_notes: List[str] = Field(default_factory=list)
    comparison_data: Dict[str, Any] = Field(default_factory=dict)