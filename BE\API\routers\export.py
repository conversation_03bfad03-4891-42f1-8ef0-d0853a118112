"""
Data Export API Endpoints

This module provides endpoints for exporting story and chapter data
in various formats including JSON, CSV, and text files.
"""

import os
import json
import csv
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import uuid

from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse

from API.models.schemas import ExportRequest, ExportResponse, APIResponse
from API.utils.database import story_service, chapter_service
from API.middleware.error_handling import DatabaseError
from API.utils.logging_config import LoggerMixin
from API.core.config import get_settings

router = APIRouter()
settings = get_settings()


class ExportController(LoggerMixin):
    """Controller for data export operations"""
    
    def __init__(self):
        self.export_dir = Path(settings.export_path)
        self.export_dir.mkdir(parents=True, exist_ok=True)
    
    async def export_data(
        self,
        request: ExportRequest,
        background_tasks: BackgroundTasks
    ) -> ExportResponse:
        """Export story/chapter data in requested format"""
        try:
            self.log_info(f"Starting data export: format={request.export_format}")
            
            # Generate unique export ID
            export_id = str(uuid.uuid4())
            
            # Validate request
            if not request.story_ids and not request.chapter_ids:
                raise HTTPException(
                    status_code=400,
                    detail="Either story_ids or chapter_ids must be provided"
                )
            
            # Start background export task
            background_tasks.add_task(
                self._background_export,
                export_id,
                request
            )
            
            # Generate download URL
            download_url = f"/api/v1/export/{export_id}/download"
            
            # Set expiration time (24 hours from now)
            expires_at = datetime.utcnow() + timedelta(hours=24)
            
            return ExportResponse(
                success=True,
                message="Export started successfully",
                export_id=export_id,
                download_url=download_url,
                expires_at=expires_at
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error starting export: {e}")
            raise DatabaseError(f"Export failed: {e}")
    
    async def download_export(self, export_id: str):
        """Download exported file"""
        try:
            self.log_info(f"Downloading export: {export_id}")
            
            # Find export file
            export_files = list(self.export_dir.glob(f"{export_id}.*"))
            
            if not export_files:
                raise HTTPException(status_code=404, detail="Export file not found or expired")
            
            export_file = export_files[0]
            
            # Check if file is expired (older than 24 hours)
            if datetime.utcnow().timestamp() - export_file.stat().st_mtime > 86400:
                export_file.unlink()  # Delete expired file
                raise HTTPException(status_code=404, detail="Export file has expired")
            
            # Determine media type
            media_type = self._get_media_type(export_file.suffix)
            
            return FileResponse(
                path=str(export_file),
                media_type=media_type,
                filename=export_file.name
            )
            
        except HTTPException:
            raise
        except Exception as e:
            self.log_error(f"Error downloading export: {e}")
            raise DatabaseError(f"Download failed: {e}")
    
    async def _background_export(self, export_id: str, request: ExportRequest):
        """Background task for data export"""
        try:
            self.log_info(f"Processing export {export_id}")
            
            # Collect data to export
            export_data = await self._collect_export_data(request)
            
            # Export based on format
            if request.export_format == "json":
                file_path = await self._export_json(export_id, export_data)
            elif request.export_format == "csv":
                file_path = await self._export_csv(export_id, export_data)
            elif request.export_format == "txt":
                file_path = await self._export_txt(export_id, export_data)
            elif request.export_format == "epub":
                file_path = await self._export_epub(export_id, export_data)
            else:
                raise ValueError(f"Unsupported export format: {request.export_format}")
            
            self.log_info(f"Export {export_id} completed: {file_path}")
            
        except Exception as e:
            self.log_error(f"Error in background export {export_id}: {e}")
            # Create error file
            error_file = self.export_dir / f"{export_id}.error"
            with open(error_file, 'w') as f:
                f.write(f"Export failed: {e}")
    
    async def _collect_export_data(self, request: ExportRequest) -> Dict[str, Any]:
        """Collect data for export based on request"""
        export_data = {
            "stories": [],
            "chapters": [],
            "metadata": {
                "exported_at": datetime.utcnow().isoformat(),
                "export_format": request.export_format,
                "include_enhanced": request.include_enhanced,
                "include_original": request.include_original,
                "include_metadata": request.include_metadata
            }
        }
        
        # Collect stories
        if request.story_ids:
            for story_id in request.story_ids:
                story = await story_service.find_by_id(story_id)
                if story:
                    story_data = self._format_story_for_export(story, request)
                    
                    # Get chapters for this story
                    chapters, _ = await chapter_service.find_by_story(story_id, page_size=10000)
                    story_chapters = []
                    
                    for chapter in chapters:
                        chapter_data = self._format_chapter_for_export(chapter, request)
                        story_chapters.append(chapter_data)
                    
                    story_data["chapters"] = story_chapters
                    export_data["stories"].append(story_data)
        
        # Collect individual chapters
        if request.chapter_ids:
            for chapter_id in request.chapter_ids:
                chapter = await chapter_service.find_by_id(chapter_id)
                if chapter:
                    chapter_data = self._format_chapter_for_export(chapter, request)
                    export_data["chapters"].append(chapter_data)
        
        return export_data
    
    def _format_story_for_export(self, story: Dict[str, Any], request: ExportRequest) -> Dict[str, Any]:
        """Format story data for export"""
        story_data = {
            "id": str(story["_id"]),
            "title": story["title"],
            "url": story["url"]
        }
        
        if request.include_metadata:
            story_data.update({
                "author": story.get("metadata", {}).get("author"),
                "description": story.get("metadata", {}).get("description"),
                "genres": story.get("metadata", {}).get("genres", []),
                "status": story.get("metadata", {}).get("status"),
                "total_chapters_scraped": story.get("total_chapters_scraped", 0),
                "total_chapters_enhanced": story.get("total_chapters_enhanced", 0),
                "created_at": story["created_at"].isoformat(),
                "updated_at": story["updated_at"].isoformat()
            })
        
        return story_data
    
    def _format_chapter_for_export(self, chapter: Dict[str, Any], request: ExportRequest) -> Dict[str, Any]:
        """Format chapter data for export"""
        chapter_data = {
            "id": str(chapter["_id"]),
            "story_id": str(chapter["story_id"]),
            "chapter_number": chapter["chapter_number"],
            "title": chapter["title"],
            "url": chapter["url"]
        }
        
        # Add content based on request
        if request.include_original and chapter.get("original_content"):
            chapter_data["original_content"] = chapter["original_content"]
        
        if request.include_enhanced and chapter.get("enhanced_content"):
            chapter_data["enhanced_content"] = chapter["enhanced_content"]
        
        if request.include_metadata:
            chapter_data.update({
                "is_scraped": chapter.get("is_scraped", False),
                "is_enhanced": chapter.get("is_enhanced", False),
                "word_count": chapter.get("metadata", {}).get("word_count"),
                "created_at": chapter["created_at"].isoformat(),
                "updated_at": chapter["updated_at"].isoformat()
            })
        
        return chapter_data
    
    async def _export_json(self, export_id: str, data: Dict[str, Any]) -> Path:
        """Export data as JSON"""
        file_path = self.export_dir / f"{export_id}.json"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return file_path
    
    async def _export_csv(self, export_id: str, data: Dict[str, Any]) -> Path:
        """Export data as CSV (stories and chapters in separate files, then zip)"""
        zip_path = self.export_dir / f"{export_id}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Export stories CSV
            if data["stories"]:
                stories_csv = self.export_dir / f"{export_id}_stories.csv"
                with open(stories_csv, 'w', newline='', encoding='utf-8') as f:
                    if data["stories"]:
                        writer = csv.DictWriter(f, fieldnames=data["stories"][0].keys())
                        writer.writeheader()
                        for story in data["stories"]:
                            # Remove chapters for CSV export
                            story_copy = story.copy()
                            story_copy.pop("chapters", None)
                            writer.writerow(story_copy)
                
                zipf.write(stories_csv, "stories.csv")
                stories_csv.unlink()  # Clean up temp file
            
            # Export chapters CSV
            all_chapters = []
            for story in data["stories"]:
                all_chapters.extend(story.get("chapters", []))
            all_chapters.extend(data["chapters"])
            
            if all_chapters:
                chapters_csv = self.export_dir / f"{export_id}_chapters.csv"
                with open(chapters_csv, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=all_chapters[0].keys())
                    writer.writeheader()
                    writer.writerows(all_chapters)
                
                zipf.write(chapters_csv, "chapters.csv")
                chapters_csv.unlink()  # Clean up temp file
        
        return zip_path
    
    async def _export_txt(self, export_id: str, data: Dict[str, Any]) -> Path:
        """Export data as plain text"""
        file_path = self.export_dir / f"{export_id}.txt"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"Export generated at: {data['metadata']['exported_at']}\n")
            f.write("=" * 50 + "\n\n")
            
            for story in data["stories"]:
                f.write(f"STORY: {story['title']}\n")
                f.write(f"Author: {story.get('author', 'Unknown')}\n")
                f.write(f"URL: {story['url']}\n")
                if story.get("description"):
                    f.write(f"Description: {story['description']}\n")
                f.write("\n" + "-" * 30 + "\n\n")
                
                for chapter in story.get("chapters", []):
                    f.write(f"Chapter {chapter['chapter_number']}: {chapter['title']}\n")
                    f.write("=" * 40 + "\n")
                    
                    # Write content (prefer enhanced over original)
                    content = chapter.get("enhanced_content") or chapter.get("original_content", "")
                    if content:
                        f.write(content)
                        f.write("\n\n")
                    
                    f.write("-" * 40 + "\n\n")
        
        return file_path
    
    async def _export_epub(self, export_id: str, data: Dict[str, Any]) -> Path:
        """Export data as EPUB (simplified implementation)"""
        # This would require the ebooklib library
        # For now, we'll create a simple HTML-based export
        file_path = self.export_dir / f"{export_id}.html"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("""<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Exported Stories</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        .story { margin-bottom: 50px; }
        .chapter { margin-bottom: 30px; }
        .chapter-title { font-size: 1.2em; font-weight: bold; margin-bottom: 10px; }
        .content { text-align: justify; }
    </style>
</head>
<body>
""")
            
            for story in data["stories"]:
                f.write(f'<div class="story">')
                f.write(f'<h1>{story["title"]}</h1>')
                if story.get("author"):
                    f.write(f'<p><strong>Author:</strong> {story["author"]}</p>')
                if story.get("description"):
                    f.write(f'<p><strong>Description:</strong> {story["description"]}</p>')
                
                for chapter in story.get("chapters", []):
                    f.write(f'<div class="chapter">')
                    f.write(f'<div class="chapter-title">Chapter {chapter["chapter_number"]}: {chapter["title"]}</div>')
                    
                    content = chapter.get("enhanced_content") or chapter.get("original_content", "")
                    if content:
                        # Simple paragraph formatting
                        paragraphs = content.split('\n\n')
                        for para in paragraphs:
                            if para.strip():
                                f.write(f'<p class="content">{para.strip()}</p>')
                    
                    f.write('</div>')
                
                f.write('</div>')
            
            f.write('</body></html>')
        
        return file_path
    
    def _get_media_type(self, extension: str) -> str:
        """Get media type for file extension"""
        media_types = {
            '.json': 'application/json',
            '.csv': 'text/csv',
            '.txt': 'text/plain',
            '.html': 'text/html',
            '.zip': 'application/zip'
        }
        return media_types.get(extension, 'application/octet-stream')
    
    async def cleanup_expired_exports(self):
        """Clean up expired export files"""
        try:
            current_time = datetime.utcnow().timestamp()
            cleaned_count = 0
            
            for file_path in self.export_dir.iterdir():
                if file_path.is_file():
                    # Check if file is older than 24 hours
                    if current_time - file_path.stat().st_mtime > 86400:
                        file_path.unlink()
                        cleaned_count += 1
            
            self.log_info(f"Cleaned up {cleaned_count} expired export files")
            return cleaned_count
            
        except Exception as e:
            self.log_error(f"Error cleaning up expired exports: {e}")
            return 0


# ============================================================================
# Router Endpoints
# ============================================================================

controller = ExportController()


@router.post("/", response_model=ExportResponse)
async def export_data(
    request: ExportRequest,
    background_tasks: BackgroundTasks
):
    """
    Export story and chapter data
    
    This endpoint exports story and chapter data in the requested format.
    The export is processed in the background and a download URL is provided.
    """
    return await controller.export_data(request, background_tasks)


@router.get("/{export_id}/download")
async def download_export(export_id: str):
    """
    Download exported file
    
    This endpoint allows downloading of previously exported data files.
    Files expire after 24 hours.
    """
    return await controller.download_export(export_id)


@router.post("/cleanup", response_model=APIResponse)
async def cleanup_expired_exports():
    """
    Clean up expired export files
    
    This endpoint removes export files that are older than 24 hours
    to free up disk space.
    """
    cleaned_count = await controller.cleanup_expired_exports()
    
    return APIResponse(
        success=True,
        message=f"Cleaned up {cleaned_count} expired export files"
    )
