'use client';

import { useState, useEffect, useCallback } from 'react';
import { Chapter, ChaptersResponse } from '@/types/story';
import { fetchChaptersByStoryId } from '@/services/storyService';

interface ChaptersCacheEntry {
  chapters: Chapter[];
  totalChapters: number;
  lastFetched: number;
  isComplete: boolean;
}

interface ChapterFilters {
  enhanced_only?: boolean;
  scraped_only?: boolean;
}

const chaptersCache = new Map<string, ChaptersCacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useChaptersCache = (storyId: string) => {
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [totalChapters, setTotalChapters] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [pageSize, setPageSize] = useState(20);

  const isCacheValid = useCallback((entry: ChaptersCacheEntry) => {
    return Date.now() - entry.lastFetched < CACHE_DURATION;
  }, []);

  const loadChapters = useCallback(async (
    page: number = 1,
    pageSizeParam: number = 20,
    forceRefresh: boolean = false,
    filters?: ChapterFilters
  ) => {
    const filterKey = filters ? `${filters.enhanced_only || false}-${filters.scraped_only || false}` : 'none';
    const cacheKey = `${storyId}-${page}-${pageSizeParam}-${filterKey}`;
    const cachedEntry = chaptersCache.get(cacheKey);

    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cachedEntry && isCacheValid(cachedEntry)) {
      setChapters(cachedEntry.chapters);
      setTotalChapters(cachedEntry.totalChapters);
      setCurrentPage(page);
      setPageSize(pageSizeParam);
      setTotalPages(Math.ceil(cachedEntry.totalChapters / pageSizeParam));
      return cachedEntry;
    }

    try {
      setLoading(true);
      setError('');

      const response = await fetchChaptersByStoryId(storyId, page, pageSizeParam, filters);
      
      const newEntry: ChaptersCacheEntry = {
        chapters: response.data,
        totalChapters: response.pagination.total_items,
        lastFetched: Date.now(),
        isComplete: !response.pagination.has_next
      };

      // Cache the result
      chaptersCache.set(cacheKey, newEntry);
      
      setChapters(response.data);
      setTotalChapters(response.pagination.total_items);
      setCurrentPage(page);
      setPageSize(pageSizeParam);
      setTotalPages(Math.ceil(response.pagination.total_items / pageSizeParam));
      
      return newEntry;
    } catch (err) {
      const errorMessage = 'Unable to load chapter list';
      setError(errorMessage);
      console.error('Error loading chapters:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [storyId, isCacheValid]);

  // Navigate to specific page
  const goToPage = useCallback(async (page: number, filters?: ChapterFilters, customPageSize?: number) => {
    const effectivePageSize = customPageSize || pageSize;
    await loadChapters(page, effectivePageSize, false, filters);
  }, [loadChapters, pageSize]);

  const clearCache = useCallback(() => {
    // Clear all cache entries for this story
    Array.from(chaptersCache.keys()).forEach(key => {
      if (key.startsWith(storyId)) {
        chaptersCache.delete(key);
      }
    });
  }, [storyId]);

  // Auto-load chapters on mount if not already loaded
  useEffect(() => {
    if (chapters.length === 0 && !loading) {
      loadChapters();
    }
  }, [storyId, chapters.length, loading, loadChapters]);

  return {
    chapters,
    loading,
    error,
    totalChapters,
    currentPage,
    totalPages,
    pageSize,
    loadChapters,
    goToPage,
    clearCache,
    setPageSize
  };
};

export default useChaptersCache;