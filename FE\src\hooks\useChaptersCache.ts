'use client';

import { useState, useEffect, useCallback } from 'react';
import { Chapter, ChaptersResponse } from '@/types/story';
import { fetchChaptersByStoryId } from '@/services/storyService';

interface ChaptersCacheEntry {
  chapters: Chapter[];
  totalChapters: number;
  lastFetched: number;
  isComplete: boolean;
}

interface ChapterFilters {
  enhanced_only?: boolean;
  scraped_only?: boolean;
}

const chaptersCache = new Map<string, ChaptersCacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const useChaptersCache = (storyId: string) => {
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [totalChapters, setTotalChapters] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [pageSize, setPageSize] = useState(50);

  const isCacheValid = useCallback((entry: ChaptersCacheEntry) => {
    return Date.now() - entry.lastFetched < CACHE_DURATION;
  }, []);

  const loadChapters = useCallback(async (
    page: number = 1,
    pageSizeParam: number = 50,
    forceRefresh: boolean = false,
    filters?: ChapterFilters
  ) => {
    const filterKey = filters ? `${filters.enhanced_only || false}-${filters.scraped_only || false}` : 'none';
    const cacheKey = `${storyId}-${page}-${pageSizeParam}-${filterKey}`;
    const cachedEntry = chaptersCache.get(cacheKey);

    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cachedEntry && isCacheValid(cachedEntry)) {
      setChapters(cachedEntry.chapters);
      setTotalChapters(cachedEntry.totalChapters);
      setCurrentPage(page);
      setPageSize(pageSizeParam);
      setTotalPages(Math.ceil(cachedEntry.totalChapters / pageSizeParam));
      return cachedEntry;
    }

    try {
      setLoading(true);
      setError('');

      const response = await fetchChaptersByStoryId(storyId, page, pageSizeParam, filters);
      
      const newEntry: ChaptersCacheEntry = {
        chapters: response.data,
        totalChapters: response.pagination.total_items,
        lastFetched: Date.now(),
        isComplete: !response.pagination.has_next
      };

      // Cache the result
      chaptersCache.set(cacheKey, newEntry);
      
      setChapters(response.data);
      setTotalChapters(response.pagination.total_items);
      setCurrentPage(page);
      setPageSize(pageSizeParam);
      setTotalPages(Math.ceil(response.pagination.total_items / pageSizeParam));
      
      return newEntry;
    } catch (err) {
      const errorMessage = 'Không thể tải danh sách chương';
      setError(errorMessage);
      console.error('Error loading chapters:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [storyId, isCacheValid]);

  const loadAllChapters = useCallback(async (maxPages: number = 5) => {
    const allChaptersCacheKey = `${storyId}-all`;
    const cachedEntry = chaptersCache.get(allChaptersCacheKey);

    // Return cached data if valid
    if (cachedEntry && isCacheValid(cachedEntry) && cachedEntry.isComplete) {
      setChapters(cachedEntry.chapters);
      setTotalChapters(cachedEntry.totalChapters);
      return cachedEntry.chapters;
    }

    // If we have partial cached data that's still valid, return it while loading more
    if (cachedEntry && isCacheValid(cachedEntry) && cachedEntry.chapters.length > 0) {
      setChapters(cachedEntry.chapters);
      setTotalChapters(cachedEntry.totalChapters);
    }

    try {
      setLoading(true);
      setError('');
      
      // Load first page to get total count
      const firstResponse = await fetchChaptersByStoryId(storyId, 1, 100);
      let allChapters = [...firstResponse.data];
      
      // Load additional pages if needed
      if (firstResponse.pagination.has_next) {
        const totalPages = Math.ceil(firstResponse.pagination.total_items / 100);
        const pagesToLoad = Math.min(totalPages, maxPages) - 1;
        
        const promises = [];
        for (let page = 2; page <= pagesToLoad + 1; page++) {
          promises.push(fetchChaptersByStoryId(storyId, page, 100));
        }
        
        const additionalResponses = await Promise.all(promises);
        const additionalChapters = additionalResponses.flatMap(res => res.data);
        allChapters = [...allChapters, ...additionalChapters];
      }

      const newEntry: ChaptersCacheEntry = {
        chapters: allChapters,
        totalChapters: firstResponse.pagination.total_items,
        lastFetched: Date.now(),
        isComplete: allChapters.length >= firstResponse.pagination.total_items
      };

      // Cache the result
      chaptersCache.set(allChaptersCacheKey, newEntry);
      
      setChapters(allChapters);
      setTotalChapters(firstResponse.pagination.total_items);
      
      return allChapters;
    } catch (err) {
      const errorMessage = 'Không thể tải danh sách chương';
      setError(errorMessage);
      console.error('Error loading all chapters:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [storyId, isCacheValid]);

  const loadNavigationChapters = useCallback(async (currentChapterNumber: number) => {
    const navCacheKey = `${storyId}-nav-${currentChapterNumber}`;
    const cachedEntry = chaptersCache.get(navCacheKey);

    // Return cached data if valid
    if (cachedEntry && isCacheValid(cachedEntry)) {
      return cachedEntry.chapters;
    }

    try {
      // Load a small range around current chapter for navigation
      const startChapter = Math.max(1, currentChapterNumber - 2);
      const response = await fetchChaptersByStoryId(storyId, 1, 5, {
        // This would need backend support for chapter range filtering
      });
      
      const navChapters = response.data.filter(ch => 
        ch.chapter_number >= startChapter && 
        ch.chapter_number <= currentChapterNumber + 2
      );

      const newEntry: ChaptersCacheEntry = {
        chapters: navChapters,
        totalChapters: response.pagination.total_items,
        lastFetched: Date.now(),
        isComplete: false
      };

      chaptersCache.set(navCacheKey, newEntry);
      return navChapters;
    } catch (err) {
      console.error('Error loading navigation chapters:', err);
      return [];
    }
  }, [storyId, isCacheValid]);

  const clearCache = useCallback(() => {
    // Clear all cache entries for this story
    Array.from(chaptersCache.keys()).forEach(key => {
      if (key.startsWith(storyId)) {
        chaptersCache.delete(key);
      }
    });
  }, [storyId]);

  return {
    chapters,
    loading,
    error,
    totalChapters,
    currentPage,
    totalPages,
    pageSize,
    loadChapters,
    loadAllChapters,
    loadNavigationChapters,
    clearCache
  };
};

export default useChaptersCache;