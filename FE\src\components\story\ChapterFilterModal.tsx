'use client';

import React from 'react';
import { <PERSON>alog, DialogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter, X } from 'lucide-react';
import { ChapterFilter } from './ChapterStatusFilter';

interface ChapterFilterModalProps {
  filter: ChapterFilter;
  onFilterChange: (filter: ChapterFilter) => void;
  stats: {
    total: number;
    scraped: number;
    unscraped: number;
    selected: number;
  };
  className?: string;
}

const ChapterFilterModal: React.FC<ChapterFilterModalProps> = ({
  filter,
  onFilterChange,
  stats,
  className = ''
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [tempFilter, setTempFilter] = React.useState<ChapterFilter>(filter);

  const handleApplyFilters = () => {
    onFilterChange(tempFilter);
    setIsOpen(false);
  };

  const handleClearAllFilters = () => {
    const clearedFilter = { scraped: null, enhanced: null };
    setTempFilter(clearedFilter);
    onFilterChange(clearedFilter);
    setIsOpen(false);
  };

  const getActiveFilters = () => {
    const activeFilters = [];
    if (filter.scraped !== null) {
      activeFilters.push({
        key: 'scraped',
        label: filter.scraped ? 'Đã cào' : 'Chưa cào',
        color: 'bg-green-500/20 text-green-400'
      });
    }
    if (filter.enhanced !== null) {
      activeFilters.push({
        key: 'enhanced',
        label: filter.enhanced ? 'Đã nâng cấp' : 'Chưa nâng cấp',
        color: 'bg-blue-500/20 text-blue-400'
      });
    }
    return activeFilters;
  };

  const removeFilter = (filterKey: 'scraped' | 'enhanced') => {
    const newFilter = { ...filter, [filterKey]: null };
    onFilterChange(newFilter);
  };

  const activeFilters = getActiveFilters();

  return (
    <div className={className}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4" />
            Lọc
            {activeFilters.length > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 px-1.5 text-xs">
                {activeFilters.length}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Lọc chương</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Scraped Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Trạng thái cào:</label>
              <Select
                value={tempFilter.scraped === null ? 'all' : tempFilter.scraped ? 'true' : 'false'}
                onValueChange={(value) => {
                  const scraped = value === 'all' ? null : value === 'true';
                  setTempFilter({ ...tempFilter, scraped });
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="true">Đã cào</SelectItem>
                  <SelectItem value="false">Chưa cào</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Enhanced Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Trạng thái nâng cấp:</label>
              <Select
                value={tempFilter.enhanced === null ? 'all' : tempFilter.enhanced ? 'true' : 'false'}
                onValueChange={(value) => {
                  const enhanced = value === 'all' ? null : value === 'true';
                  setTempFilter({ ...tempFilter, enhanced });
                }}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="true">Đã nâng cấp</SelectItem>
                  <SelectItem value="false">Chưa nâng cấp</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              <Button onClick={handleClearAllFilters} variant="outline" className="flex-1">
                Xóa tất cả
              </Button>
              <Button onClick={handleApplyFilters} className="flex-1">
                Áp dụng
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {activeFilters.map((activeFilter) => (
            <Badge
              key={activeFilter.key}
              variant="secondary"
              className={`${activeFilter.color} gap-1`}
            >
              {activeFilter.label}
              <button
                onClick={() => removeFilter(activeFilter.key as 'scraped' | 'enhanced')}
                className="hover:bg-white/20 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChapterFilterModal;