import sys
import asyncio
import uvicorn
from pathlib import Path

# Add project root to the Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
from API.core.app import create_app

app = create_app()

if __name__ == "__main__":
    # For development, run with: python -m API.main
    # For production, use a process manager like Gunicorn or Hypercorn
    uvicorn.run("API.main:app", host="0.0.0.0", port=8002, reload=False)