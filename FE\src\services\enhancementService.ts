/**
 * Chapter Enhancement Service
 * 
 * Provides functionality for enhancing chapter content using AI/ML services.
 * This service handles batch enhancement operations with progress tracking.
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
const API_PREFIX = '/api/v1/enhancement';

// Types for enhancement operations
export interface EnhancementRequest {
  chapter_ids: string[];
  enhancement_type?: 'grammar' | 'style' | 'full';
  max_concurrent?: number;
  background?: boolean;
}

export interface EnhancementResult {
  chapter_id: string;
  chapter_number: number;
  status: 'success' | 'failed' | 'skipped';
  error_message?: string;
  enhanced_at?: string;
  enhancement_type?: string;
  word_count_before?: number;
  word_count_after?: number;
}

export interface EnhancementResponse {
  job_id: string;
  success: boolean;
  message: string;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  skipped_count: number;
  results: EnhancementResult[];
  started_at: string;
  completed_at?: string;
}

export interface EnhancementProgress {
  job_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress_percentage: number;
  total_chapters: number;
  completed_chapters: number;
  failed_chapters: number;
  current_chapter?: string;
  estimated_completion?: string;
  results: EnhancementResult[];
}

// Helper function to make API requests
const makeApiRequest = async <T>(endpoint: string, options: RequestInit = {}): Promise<T> => {
  const url = `${API_BASE_URL}${API_PREFIX}${endpoint}`;
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`Enhancement API request failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

class EnhancementService {
  private activeJobs = new Map<string, EnhancementProgress>();
  private progressCallbacks = new Map<string, (progress: EnhancementProgress) => void>();
  private pollingIntervals = new Map<string, NodeJS.Timeout>();

  /**
   * Start batch enhancement for specific chapters
   */
  async startBatchEnhancement(
    chapterIds: string[],
    options: {
      enhancementType?: 'grammar' | 'style' | 'full';
      maxConcurrent?: number;
      background?: boolean;
      onProgress?: (progress: EnhancementProgress) => void;
    } = {}
  ): Promise<EnhancementResponse> {
    const request: EnhancementRequest = {
      chapter_ids: chapterIds,
      enhancement_type: options.enhancementType || 'full',
      max_concurrent: options.maxConcurrent || 2, // Lower concurrency for AI operations
      background: options.background || false
    };

    try {
      const response = await makeApiRequest<EnhancementResponse>(
        '/enhance-chapters',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request)
        }
      );

      // Store job info and start progress tracking if background
      if (request.background && options.onProgress) {
        this.startProgressPolling(response.job_id, options.onProgress);
      }

      return response;
    } catch (error) {
      console.error('Failed to start batch enhancement:', error);
      throw error;
    }
  }

  /**
   * Get enhancement progress for a specific job
   */
  async getEnhancementProgress(jobId: string): Promise<EnhancementProgress> {
    try {
      const progress = await makeApiRequest<EnhancementProgress>(
        `/progress/${jobId}`
      );
      
      // Update local cache
      this.activeJobs.set(jobId, progress);
      
      return progress;
    } catch (error) {
      console.error('Failed to get enhancement progress:', error);
      throw error;
    }
  }

  /**
   * Start polling for progress updates
   */
  startProgressPolling(
    jobId: string,
    onProgress: (progress: EnhancementProgress) => void,
    intervalMs: number = 3000 // Longer interval for AI operations
  ): void {
    // Store callback
    this.progressCallbacks.set(jobId, onProgress);

    // Clear existing interval if any
    const existingInterval = this.pollingIntervals.get(jobId);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // Start polling
    const interval = setInterval(async () => {
      try {
        const progress = await this.getEnhancementProgress(jobId);
        onProgress(progress);

        // Stop polling if job is complete
        if (['completed', 'failed', 'cancelled'].includes(progress.status)) {
          this.stopProgressPolling(jobId);
        }
      } catch (error) {
        console.error('Error polling enhancement progress:', error);
        // Continue polling on error, but notify callback
        onProgress({
          job_id: jobId,
          status: 'failed',
          progress_percentage: 0,
          total_chapters: 0,
          completed_chapters: 0,
          failed_chapters: 0,
          results: []
        });
      }
    }, intervalMs);

    this.pollingIntervals.set(jobId, interval);
  }

  /**
   * Stop polling for a specific job
   */
  stopProgressPolling(jobId: string): void {
    const interval = this.pollingIntervals.get(jobId);
    if (interval) {
      clearInterval(interval);
      this.pollingIntervals.delete(jobId);
    }
    this.progressCallbacks.delete(jobId);
  }

  /**
   * Cancel a running enhancement job
   */
  async cancelEnhancementJob(jobId: string): Promise<void> {
    try {
      await makeApiRequest(`/cancel/${jobId}`, {
        method: 'POST'
      });
      
      this.stopProgressPolling(jobId);
      this.activeJobs.delete(jobId);
    } catch (error) {
      console.error('Failed to cancel enhancement job:', error);
      throw error;
    }
  }

  /**
   * Get cached progress for a job
   */
  getCachedProgress(jobId: string): EnhancementProgress | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Cleanup all active polling
   */
  cleanup(): void {
    this.pollingIntervals.forEach((interval) => clearInterval(interval));
    this.pollingIntervals.clear();
    this.progressCallbacks.clear();
    this.activeJobs.clear();
  }

  /**
   * Batch enhance with comprehensive progress tracking
   */
  async batchEnhanceWithProgress(
    chapterIds: string[],
    options: {
      enhancementType?: 'grammar' | 'style' | 'full';
      maxConcurrent?: number;
      onProgress?: (progress: EnhancementProgress) => void;
      onComplete?: (results: EnhancementResponse) => void;
      onError?: (error: Error) => void;
    } = {}
  ): Promise<string | null> {
    try {
      // Start batch enhancement in background
      const response = await this.startBatchEnhancement(chapterIds, {
        ...options,
        background: true
      });

      const { onProgress: originalOnProgress } = options;
      
      // Enhanced progress callback that handles completion
      const enhancedOnProgress = (progress: EnhancementProgress) => {
        if (originalOnProgress) {
          originalOnProgress(progress);
        }
        
        // Check for completion
        if (progress.status === 'completed' && options.onComplete) {
          const completedResponse: EnhancementResponse = {
            job_id: progress.job_id,
            success: true,
            message: 'Batch enhancement completed',
            total_requested: progress.total_chapters,
            successful_count: progress.completed_chapters,
            failed_count: progress.failed_chapters,
            skipped_count: 0,
            results: progress.results,
            started_at: response.started_at,
            completed_at: new Date().toISOString()
          };
          options.onComplete(completedResponse);
        }
        
        // Check for errors
        if (progress.status === 'failed' && options.onError) {
          options.onError(new Error('Enhancement job failed'));
        }
      };
      
      // Start progress polling with enhanced callback
      this.startProgressPolling(response.job_id, enhancedOnProgress);
      
      return response.job_id;
    } catch (error) {
      if (options.onError) {
        options.onError(error as Error);
      }
      return null;
    }
  }
}

// Export singleton instance
export const enhancementService = new EnhancementService();

// Utility functions
export const formatEnhancementProgress = (progress: EnhancementProgress): string => {
  return `${progress.completed_chapters}/${progress.total_chapters} (${Math.round(progress.progress_percentage)}%)`;
};

export const getEnhancementStatusColor = (status: EnhancementProgress['status']): string => {
  switch (status) {
    case 'pending': return 'text-yellow-500';
    case 'running': return 'text-blue-500';
    case 'completed': return 'text-green-500';
    case 'failed': return 'text-red-500';
    case 'cancelled': return 'text-gray-500';
    default: return 'text-gray-400';
  }
};