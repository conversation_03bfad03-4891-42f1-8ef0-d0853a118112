"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, MoreHorizontal } from "lucide-react"
import { generatePageNumbers } from "@/hooks/usePagination"

export interface PaginationProps {
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  pageSizeOptions?: number[]
  onPageChange: (page: number) => void
  onPageSizeChange: (size: number) => void
  showPageSizeSelector?: boolean
  showJumpToPage?: boolean
  showItemsInfo?: boolean
  maxVisiblePages?: number
  isLoading?: boolean
  className?: string
}

export function Pagination({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  pageSizeOptions = [5, 10, 20, 50, 100],
  onPage<PERSON>hange,
  onPageSizeChange,
  showPageSizeSelector = true,
  showJumpToPage = true,
  showItemsInfo = true,
  maxVisiblePages = 7,
  isLoading = false,
  className = "",
}: PaginationProps) {
  const [jumpToPageValue, setJumpToPageValue] = useState("")

  const canGoPrevious = currentPage > 1
  const canGoNext = currentPage < totalPages

  const handleJumpToPage = () => {
    const page = Number.parseInt(jumpToPageValue)
    if (page >= 1 && page <= totalPages) {
      onPageChange(page)
      setJumpToPageValue("")
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleJumpToPage()
    }
  }

  const pageNumbers = generatePageNumbers(currentPage, totalPages, maxVisiblePages)

  const startItem = (currentPage - 1) * pageSize + 1
  const endItem = Math.min(currentPage * pageSize, totalItems)

  if (totalPages <= 0) {
    return null
  }

  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {/* Items info and jump to page */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        {showItemsInfo && (
          <div className="text-sm text-muted-foreground">
            Showing {startItem} to {endItem} of {totalItems} items
          </div>
        )}
        {/* Jump to page */}
        {showJumpToPage && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Go to page:</span>
            <Input
              type="number"
              min={1}
              max={totalPages}
              value={jumpToPageValue}
              onChange={(e) => setJumpToPageValue(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isLoading}
              className="w-20"
              placeholder="1"
              aria-label="Jump to page number"
            />
            <Button size="sm" onClick={handleJumpToPage} disabled={isLoading || !jumpToPageValue}>
              Go
            </Button>
          </div>
        )}
      </div>

      {/* Main pagination controls */}
      <div className="flex flex-col sm:flex-row items-center justify-start gap-4">
        {/* Navigation buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(1)}
            disabled={!canGoPrevious || isLoading}
            aria-label="Go to first page"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!canGoPrevious || isLoading}
            aria-label="Go to previous page"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Page numbers */}
          <div className="flex items-center gap-1 mx-2">
            {pageNumbers.map((page, index) =>
              page === "ellipsis" ? (
                <div key={`ellipsis-${index}`} className="px-2">
                  <MoreHorizontal className="h-4 w-4" />
                </div>
              ) : (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page)}
                  disabled={isLoading}
                  aria-label={`Go to page ${page}`}
                  aria-current={page === currentPage ? "page" : undefined}
                  className="min-w-[2.5rem]"
                >
                  {page}
                </Button>
              ),
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!canGoNext || isLoading}
            aria-label="Go to next page"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(totalPages)}
            disabled={!canGoNext || isLoading}
            aria-label="Go to last page"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>

        {showPageSizeSelector && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Items per page:</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => onPageSizeChange(Number.parseInt(value))}
              disabled={isLoading}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    </div>
  )
}
