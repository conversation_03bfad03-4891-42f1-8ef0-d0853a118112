"""
Job Status related API Models
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from ..database import ScrapingStatus
from .common import APIResponse

# ============================================================================
# Job Status API Models
# ============================================================================

class JobStatusResponse(APIResponse):
    """Response for job status"""
    job_id: str
    job_type: str
    status: ScrapingStatus
    progress_percentage: float
    total_items: Optional[int] = None
    completed_items: int
    failed_items: int
    errors: List[str] = []
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None