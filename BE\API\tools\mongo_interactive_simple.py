#!/usr/bin/env python3
"""
Interactive MongoDB CRUD Script for Webtruyen Container (Simple Version)
=========================================================================

This script provides a clean interface to interact with MongoDB container
using Docker exec commands for all operations. No external dependencies required.

Usage:
    python mongo_interactive_simple.py

Features:
    - Interactive menu system
    - Complete CRUD operations via Docker exec
    - Error handling and validation
    - Container connection management
    - Data export/import capabilities
    - Direct MongoDB shell integration
    - No external dependencies required
"""

import os
import sys
import json
import subprocess
import tempfile
from datetime import datetime
from typing import Dict, List, Optional, Any


class DatabaseConfig:
    """Database configuration class"""
    def __init__(self):
        self.host = "localhost"
        self.port = 27017
        self.username = "admin"
        self.password = "password123"
        self.database = "webtruyen_api"
        self.auth_source = "admin"
        self.container_name = "webtruyen_mongodb"


class MongoManager:
    """MongoDB connection and operation manager using Docker exec"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connected = False
        self.connection_string = (
            f"mongodb://{self.config.username}:{self.config.password}"
            f"@{self.config.host}:{self.config.port}/{self.config.database}"
        )
    
    def connect(self) -> bool:
        """Test MongoDB connection via Docker exec"""
        try:
            # Test connection with ping command
            result = self._execute_mongo_command("db.runCommand('ping')")
            if result and "ok" in result and result.get("ok") == 1:
                self.connected = True
                print(f"✅ Connected to MongoDB: {self.config.database}")
                return True
            else:
                print(f"❌ Connection failed: Invalid response")
                return False
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def disconnect(self):
        """Mark connection as disconnected"""
        self.connected = False
        print("✅ Disconnected from MongoDB")
    
    def _execute_mongo_command(self, command: str) -> Dict[str, Any]:
        """Execute MongoDB command via Docker exec"""
        try:
            # Use JSON.stringify to ensure proper JSON output
            json_command = f"JSON.stringify({command})"
            
            full_command = [
                "docker", "exec", "-i", self.config.container_name,
                "mongosh", self.connection_string,
                "--authenticationDatabase", self.config.auth_source,
                "--eval", json_command,
                "--quiet"
            ]
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # Try to parse JSON response
                try:
                    return json.loads(result.stdout.strip())
                except json.JSONDecodeError:
                    # Fallback: check for success patterns
                    output = result.stdout.strip()
                    if "ok: 1" in output or '"ok":1' in output:
                        return {"ok": 1}
                    # If not JSON, return as string
                    return {"result": output}
            else:
                raise Exception(f"Command failed: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("Command timed out")
        except Exception as e:
            raise Exception(f"Execution error: {e}")
    
    def _execute_mongo_script(self, script: str) -> str:
        """Execute MongoDB script via Docker exec"""
        try:
            # Create temporary file with script
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(script)
                temp_file = f.name
            
            # Copy script to container
            copy_command = [
                "docker", "cp", temp_file, 
                f"{self.config.container_name}:/tmp/mongo_script.js"
            ]
            subprocess.run(copy_command, check=True)
            
            # Execute script in container
            exec_command = [
                "docker", "exec", "-i", self.config.container_name,
                "mongosh", self.connection_string,
                "--authenticationDatabase", self.config.auth_source,
                "/tmp/mongo_script.js",
                "--quiet"
            ]
            
            result = subprocess.run(
                exec_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Clean up
            os.unlink(temp_file)
            subprocess.run([
                "docker", "exec", self.config.container_name,
                "rm", "/tmp/mongo_script.js"
            ], capture_output=True)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                raise Exception(f"Script failed: {result.stderr}")
                
        except Exception as e:
            raise Exception(f"Script execution error: {e}")
    
    def get_collections(self) -> List[str]:
        """Get list of collections"""
        if not self.connected:
            return []
        
        try:
            script = """
            var collections = db.runCommand('listCollections').cursor.firstBatch.map(c => c.name);
            print(JSON.stringify(collections));
            """
            result = self._execute_mongo_script(script)
            
            if result:
                try:
                    collections = json.loads(result)
                    return collections if isinstance(collections, list) else []
                except json.JSONDecodeError:
                    return []
            return []
            
        except Exception as e:
            print(f"❌ Error getting collections: {e}")
            return []
    
    def get_collection_stats(self, collection_name: str) -> Dict[str, Any]:
        """Get collection statistics"""
        if not self.connected:
            return {}
        
        try:
            script = f"""
            var stats = db.runCommand({{collStats: '{collection_name}'}});
            print(JSON.stringify({{
                count: stats.count || 0,
                size: stats.size || 0,
                avgObjSize: stats.avgObjSize || 0,
                indexes: stats.nindexes || 0
            }}));
            """
            result = self._execute_mongo_script(script)
            
            if result:
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return {}
            return {}
            
        except Exception as e:
            print(f"❌ Error getting stats: {e}")
            return {}


class CRUDOperations:
    """CRUD operations handler using Docker exec"""
    
    def __init__(self, mongo_manager: MongoManager):
        self.mongo = mongo_manager
    
    def create_document(self, collection_name: str, document: Dict[str, Any]) -> Optional[str]:
        """Create a new document"""
        try:
            # Convert document to JSON string for MongoDB
            doc_json = json.dumps(document, default=str)
            
            script = f"""
            var doc = {doc_json};
            var result = db.{collection_name}.insertOne(doc);
            print(JSON.stringify({{insertedId: result.insertedId.toString()}}));
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result:
                try:
                    parsed_result = json.loads(result)
                    doc_id = parsed_result.get('insertedId')
                    print(f"✅ Document created with ID: {doc_id}")
                    return doc_id
                except json.JSONDecodeError:
                    print(f"✅ Document created successfully")
                    return "success"
            else:
                print(f"❌ Failed to create document")
                return None
                
        except Exception as e:
            print(f"❌ Error creating document: {e}")
            return None
    
    def read_documents(self, collection_name: str, query: Dict[str, Any] = None, 
                      limit: int = 10, skip: int = 0) -> List[Dict[str, Any]]:
        """Read documents with optional filtering"""
        try:
            query_json = json.dumps(query or {}, default=str)
            
            script = f"""
            var query = {query_json};
            var docs = db.{collection_name}.find(query).skip({skip}).limit({limit}).toArray();
            print(JSON.stringify(docs));
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result:
                try:
                    documents = json.loads(result)
                    # Convert ObjectId to string for display
                    for doc in documents:
                        if '_id' in doc and isinstance(doc['_id'], dict) and '$oid' in doc['_id']:
                            doc['_id'] = doc['_id']['$oid']
                    
                    print(f"✅ Found {len(documents)} documents")
                    return documents
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse documents")
                    return []
            else:
                print(f"✅ Found 0 documents")
                return []
                
        except Exception as e:
            print(f"❌ Error reading documents: {e}")
            return []
    
    def read_document_by_id(self, collection_name: str, doc_id: str) -> Optional[Dict[str, Any]]:
        """Read a single document by ID"""
        try:
            script = f"""
            var doc = db.{collection_name}.findOne({{_id: ObjectId('{doc_id}')}});
            if (doc) {{
                print(JSON.stringify(doc));
            }} else {{
                print('null');
            }}
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result and result.strip() != 'null':
                try:
                    document = json.loads(result)
                    # Convert ObjectId to string for display
                    if '_id' in document and isinstance(document['_id'], dict) and '$oid' in document['_id']:
                        document['_id'] = document['_id']['$oid']
                    
                    print(f"✅ Document found")
                    return document
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse document")
                    return None
            else:
                print(f"❌ Document not found with ID: {doc_id}")
                return None
                
        except Exception as e:
            print(f"❌ Error reading document: {e}")
            return None
    
    def update_document(self, collection_name: str, doc_id: str, 
                       update_data: Dict[str, Any]) -> bool:
        """Update a document by ID"""
        try:
            update_json = json.dumps(update_data, default=str)
            
            script = f"""
            var updateData = {update_json};
            var result = db.{collection_name}.updateOne(
                {{_id: ObjectId('{doc_id}')}},
                {{$set: updateData}}
            );
            print(JSON.stringify({{matchedCount: result.matchedCount, modifiedCount: result.modifiedCount}}));
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result:
                try:
                    parsed_result = json.loads(result)
                    matched_count = parsed_result.get('matchedCount', 0)
                    modified_count = parsed_result.get('modifiedCount', 0)
                    
                    if matched_count > 0:
                        print(f"✅ Document updated (modified: {modified_count})")
                        return True
                    else:
                        print(f"❌ Document not found with ID: {doc_id}")
                        return False
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse update result")
                    return False
            else:
                print(f"❌ Failed to update document")
                return False
                
        except Exception as e:
            print(f"❌ Error updating document: {e}")
            return False
    
    def delete_document(self, collection_name: str, doc_id: str) -> bool:
        """Delete a document by ID"""
        try:
            script = f"""
            var result = db.{collection_name}.deleteOne({{_id: ObjectId('{doc_id}')}});
            print(JSON.stringify({{deletedCount: result.deletedCount}}));
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result:
                try:
                    parsed_result = json.loads(result)
                    deleted_count = parsed_result.get('deletedCount', 0)
                    
                    if deleted_count > 0:
                        print(f"✅ Document deleted")
                        return True
                    else:
                        print(f"❌ Document not found with ID: {doc_id}")
                        return False
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse delete result")
                    return False
            else:
                print(f"❌ Failed to delete document")
                return False
                
        except Exception as e:
            print(f"❌ Error deleting document: {e}")
            return False
    
    def count_documents(self, collection_name: str, query: Dict[str, Any] = None) -> int:
        """Count documents matching query"""
        try:
            query_json = json.dumps(query or {}, default=str)
            
            script = f"""
            var query = {query_json};
            var count = db.{collection_name}.countDocuments(query);
            print(JSON.stringify({{count: count}}));
            """
            
            result = self.mongo._execute_mongo_script(script)
            
            if result:
                try:
                    parsed_result = json.loads(result)
                    count = parsed_result.get('count', 0)
                    print(f"✅ Document count: {count}")
                    return count
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse count result")
                    return 0
            else:
                print(f"❌ Failed to count documents")
                return 0
                
        except Exception as e:
            print(f"❌ Error counting documents: {e}")
            return 0


class InteractiveMenu:
    """Interactive menu system"""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self.mongo_manager = MongoManager(self.config)
        self.crud = CRUDOperations(self.mongo_manager)
        self.connected = False
    
    def print_header(self):
        """Print application header"""
        print("\n" + "="*60)
        print("🍃 MongoDB Interactive CRUD Tool (Docker Exec)")
        print("   Webtruyen Container Manager")
        print("="*60)
    
    def print_menu(self):
        """Print main menu"""
        status = "🟢 Connected" if self.connected else "🔴 Disconnected"
        print(f"\n📊 Status: {status}")
        print(f"🗄️  Database: {self.config.database}")
        print(f"📦 Container: {self.config.container_name}")
        print("\n📋 Main Menu:")
        print("1. 🔌 Connect to MongoDB")
        print("2. 📊 Show Database Info")
        print("3. 📝 CRUD Operations")
        print("4. 🐚 Open MongoDB Shell")
        print("5. 📤 Export Data")
        print("6. 📥 Import Data")
        print("7. 🐳 Container Management")
        print("8. ⚙️  Settings")
        print("9. 🚪 Exit")
        print("-" * 30)
    
    def crud_menu(self):
        """CRUD operations menu"""
        if not self.connected:
            print("❌ Please connect to MongoDB first")
            return
        
        print("\n📝 CRUD Operations:")
        print("1. ➕ Create Document")
        print("2. 📖 Read Documents")
        print("3. 🔍 Find Document by ID")
        print("4. ✏️  Update Document")
        print("5. 🗑️  Delete Document")
        print("6. 🔢 Count Documents")
        print("7. 🔙 Back to Main Menu")
        
        choice = input("\nSelect operation (1-7): ").strip()
        
        if choice == "1":
            self.create_document_interactive()
        elif choice == "2":
            self.read_documents_interactive()
        elif choice == "3":
            self.find_document_by_id_interactive()
        elif choice == "4":
            self.update_document_interactive()
        elif choice == "5":
            self.delete_document_interactive()
        elif choice == "6":
            self.count_documents_interactive()
        elif choice == "7":
            return
        else:
            print("❌ Invalid choice")
    
    def create_document_interactive(self):
        """Interactive document creation"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        print("\nEnter document data as JSON:")
        print("Example: {\"name\": \"Test\", \"age\": 25}")
        json_input = input("JSON: ").strip()
        
        try:
            document = json.loads(json_input)
            document['created_at'] = datetime.now().isoformat()
            self.crud.create_document(collection_name, document)
        except json.JSONDecodeError:
            print("❌ Invalid JSON format")
    
    def read_documents_interactive(self):
        """Interactive document reading"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        limit = input("Enter limit (default 10): ").strip()
        limit = int(limit) if limit.isdigit() else 10
        
        skip = input("Enter skip (default 0): ").strip()
        skip = int(skip) if skip.isdigit() else 0
        
        print("\nEnter query filter (JSON, or press Enter for all):")
        query_input = input("Query: ").strip()
        
        query = {}
        if query_input:
            try:
                query = json.loads(query_input)
            except json.JSONDecodeError:
                print("❌ Invalid JSON format, using empty query")
        
        documents = self.crud.read_documents(collection_name, query, limit, skip)
        
        if documents:
            print(f"\n📋 Documents ({len(documents)}):")
            for i, doc in enumerate(documents, 1):
                print(f"\n{i}. ID: {doc['_id']}")
                for key, value in doc.items():
                    if key != '_id':
                        print(f"   {key}: {value}")
    
    def find_document_by_id_interactive(self):
        """Interactive document finding by ID"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        doc_id = input("Enter document ID: ").strip()
        if not doc_id:
            print("❌ Document ID cannot be empty")
            return
        
        document = self.crud.read_document_by_id(collection_name, doc_id)
        
        if document:
            print(f"\n📄 Document Details:")
            for key, value in document.items():
                print(f"   {key}: {value}")
    
    def update_document_interactive(self):
        """Interactive document updating"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        doc_id = input("Enter document ID: ").strip()
        if not doc_id:
            print("❌ Document ID cannot be empty")
            return
        
        print("\nEnter update data as JSON:")
        print("Example: {\"name\": \"Updated Name\", \"status\": \"active\"}")
        json_input = input("JSON: ").strip()
        
        try:
            update_data = json.loads(json_input)
            update_data['updated_at'] = datetime.now().isoformat()
            self.crud.update_document(collection_name, doc_id, update_data)
        except json.JSONDecodeError:
            print("❌ Invalid JSON format")
    
    def delete_document_interactive(self):
        """Interactive document deletion"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        doc_id = input("Enter document ID: ").strip()
        if not doc_id:
            print("❌ Document ID cannot be empty")
            return
        
        confirm = input(f"⚠️  Are you sure you want to delete document {doc_id}? (y/N): ").strip().lower()
        if confirm == 'y':
            self.crud.delete_document(collection_name, doc_id)
        else:
            print("❌ Deletion cancelled")
    
    def count_documents_interactive(self):
        """Interactive document counting"""
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        print("\nEnter query filter (JSON, or press Enter for all):")
        query_input = input("Query: ").strip()
        
        query = {}
        if query_input:
            try:
                query = json.loads(query_input)
            except json.JSONDecodeError:
                print("❌ Invalid JSON format, using empty query")
        
        self.crud.count_documents(collection_name, query)
    
    def show_database_info(self):
        """Show database information"""
        if not self.connected:
            print("❌ Please connect to MongoDB first")
            return
        
        collections = self.mongo_manager.get_collections()
        print(f"\n🗄️  Database: {self.config.database}")
        print(f"📊 Collections: {len(collections)}")
        
        if collections:
            print("\n📋 Collection Details:")
            for collection in collections:
                stats = self.mongo_manager.get_collection_stats(collection)
                print(f"   📁 {collection}")
                print(f"      Documents: {stats.get('count', 0)}")
                print(f"      Size: {stats.get('size', 0)} bytes")
                print(f"      Indexes: {stats.get('indexes', 0)}")
    
    def open_shell(self):
        """Open MongoDB shell via Docker exec"""
        print("\n🐚 Opening MongoDB Shell...")
        
        if not self.connected:
            print("❌ Please connect to MongoDB first")
            return
        
        try:
            print("💡 Tip: Use 'exit' or Ctrl+C to return to the interactive menu")
            print("📋 Common commands:")
            print("   - show collections")
            print("   - db.collection_name.find()")
            print("   - db.collection_name.countDocuments({})")
            print("" + "-" * 50)
            
            # Open interactive MongoDB shell
            command = [
                "docker", "exec", "-it", self.config.container_name,
                "mongosh", self.mongo_manager.connection_string,
                "--authenticationDatabase", self.config.auth_source
            ]
            
            subprocess.run(command)
            
        except KeyboardInterrupt:
            print("\n✅ Returned to interactive menu")
        except Exception as e:
            print(f"❌ Error opening shell: {e}")

    def container_management(self):
        """Container management options"""
        print("\n🐳 Container Management:")
        print("1. 📊 Check Container Status")
        print("2. 🚀 Start Container")
        print("3. 🛑 Stop Container")
        print("4. 🔄 Restart Container")
        print("5. 📋 Show Container Logs")
        print("6. 🔙 Back to Main Menu")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == "1":
            self.check_container_status()
        elif choice == "2":
            self.start_container()
        elif choice == "3":
            self.stop_container()
        elif choice == "4":
            self.restart_container()
        elif choice == "5":
            self.show_container_logs()
        elif choice == "6":
            return
        else:
            print("❌ Invalid choice")
    
    def check_container_status(self):
        """Check container status"""
        try:
            result = subprocess.run(
                ['docker', 'ps', '-f', f'name={self.config.container_name}'],
                capture_output=True, text=True
            )
            if self.config.container_name in result.stdout:
                print(f"✅ Container {self.config.container_name} is running")
            else:
                print(f"❌ Container {self.config.container_name} is not running")
        except Exception as e:
            print(f"❌ Error checking container status: {e}")
    
    def start_container(self):
        """Start container"""
        try:
            result = subprocess.run(
                ['docker', 'start', self.config.container_name],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ Container {self.config.container_name} started")
            else:
                print(f"❌ Failed to start container: {result.stderr}")
        except Exception as e:
            print(f"❌ Error starting container: {e}")
    
    def stop_container(self):
        """Stop container"""
        try:
            result = subprocess.run(
                ['docker', 'stop', self.config.container_name],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ Container {self.config.container_name} stopped")
            else:
                print(f"❌ Failed to stop container: {result.stderr}")
        except Exception as e:
            print(f"❌ Error stopping container: {e}")
    
    def restart_container(self):
        """Restart container"""
        try:
            result = subprocess.run(
                ['docker', 'restart', self.config.container_name],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ Container {self.config.container_name} restarted")
            else:
                print(f"❌ Failed to restart container: {result.stderr}")
        except Exception as e:
            print(f"❌ Error restarting container: {e}")
    
    def show_container_logs(self):
        """Show container logs"""
        try:
            lines = input("Enter number of log lines to show (default 50): ").strip()
            lines = lines if lines.isdigit() else "50"
            
            result = subprocess.run(
                ['docker', 'logs', '--tail', lines, self.config.container_name],
                capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"\n📋 Container Logs (last {lines} lines):")
                print("-" * 50)
                print(result.stdout)
            else:
                print(f"❌ Failed to get logs: {result.stderr}")
        except Exception as e:
            print(f"❌ Error getting logs: {e}")
    
    def export_data(self):
        """Export collection data"""
        if not self.connected:
            print("❌ Please connect to MongoDB first")
            return
        
        collection_name = input("Enter collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        filename = input(f"Enter filename (default: {collection_name}.json): ").strip()
        filename = filename if filename else f"{collection_name}.json"
        
        documents = self.crud.read_documents(collection_name, limit=1000)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(documents, f, indent=2, ensure_ascii=False)
            print(f"✅ Data exported to {filename}")
        except Exception as e:
            print(f"❌ Error exporting data: {e}")
    
    def import_data(self):
        """Import data from JSON file"""
        if not self.connected:
            print("❌ Please connect to MongoDB first")
            return
        
        filename = input("Enter JSON filename to import: ").strip()
        if not filename:
            print("❌ Filename cannot be empty")
            return
        
        if not os.path.exists(filename):
            print(f"❌ File not found: {filename}")
            return
        
        collection_name = input("Enter target collection name: ").strip()
        if not collection_name:
            print("❌ Collection name cannot be empty")
            return
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                print("❌ JSON file must contain an array of documents")
                return
            
            success_count = 0
            for doc in data:
                # Remove _id if it exists to let MongoDB generate new ones
                if '_id' in doc:
                    del doc['_id']
                doc['imported_at'] = datetime.now().isoformat()
                
                if self.crud.create_document(collection_name, doc):
                    success_count += 1
            
            print(f"✅ Imported {success_count}/{len(data)} documents")
            
        except Exception as e:
            print(f"❌ Error importing data: {e}")
    
    def run(self):
        """Main application loop"""
        self.print_header()
        
        while True:
            self.print_menu()
            choice = input("\nSelect option (1-9): ").strip()
            
            if choice == "1":
                self.connected = self.mongo_manager.connect()
            elif choice == "2":
                self.show_database_info()
            elif choice == "3":
                self.crud_menu()
            elif choice == "4":
                self.open_shell()
            elif choice == "5":
                self.export_data()
            elif choice == "6":
                self.import_data()
            elif choice == "7":
                self.container_management()
            elif choice == "8":
                print("⚙️  Settings - Coming soon!")
            elif choice == "9":
                print("👋 Goodbye!")
                if self.connected:
                    self.mongo_manager.disconnect()
                break
            else:
                print("❌ Invalid choice. Please try again.")
            
            input("\nPress Enter to continue...")


if __name__ == "__main__":
    try:
        app = InteractiveMenu()
        app.run()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

