"use client"

import { useState, useMemo, useCallback } from "react"

export interface PaginationConfig {
  initialPage?: number
  initialPageSize?: number
  pageSizeOptions?: number[]
}

export interface PaginationState {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

export interface PaginationActions {
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  nextPage: () => void
  previousPage: () => void
  goToFirstPage: () => void
  goToLastPage: () => void
  canGoNext: boolean
  canGoPrevious: boolean
}

export function usePagination(totalItems: number, config: PaginationConfig = {}): PaginationState & PaginationActions {
  const { initialPage = 1, initialPageSize = 10, pageSizeOptions = [5, 10, 20, 50, 100] } = config

  const [currentPage, setCurrentPage] = useState(initialPage)
  const [pageSize, setPageSizeState] = useState(initialPageSize)

  const totalPages = useMemo(() => {
    return Math.ceil(totalItems / pageSize)
  }, [totalItems, pageSize])

  const canGoNext = currentPage < totalPages
  const canGoPrevious = currentPage > 1

  const setPage = useCallback(
    (page: number) => {
      const clampedPage = Math.max(1, Math.min(page, totalPages))
      setCurrentPage(clampedPage)
    },
    [totalPages],
  )

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size)
    // Reset to first page when changing page size
    setCurrentPage(1)
  }, [])

  const nextPage = useCallback(() => {
    if (canGoNext) {
      setCurrentPage((prev) => prev + 1)
    }
  }, [canGoNext])

  const previousPage = useCallback(() => {
    if (canGoPrevious) {
      setCurrentPage((prev) => prev - 1)
    }
  }, [canGoPrevious])

  const goToFirstPage = useCallback(() => {
    setCurrentPage(1)
  }, [])

  const goToLastPage = useCallback(() => {
    setCurrentPage(totalPages)
  }, [totalPages])

  return {
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    setPage,
    setPageSize,
    nextPage,
    previousPage,
    goToFirstPage,
    goToLastPage,
    canGoNext,
    canGoPrevious,
  }
}

// Helper function to get paginated data from an array
export function getPaginatedData<T>(data: T[], currentPage: number, pageSize: number): T[] {
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  return data.slice(startIndex, endIndex)
}

// Helper function to generate page numbers for display
export function generatePageNumbers(currentPage: number, totalPages: number, maxVisible = 7): (number | "ellipsis")[] {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  const pages: (number | "ellipsis")[] = []
  const halfVisible = Math.floor(maxVisible / 2)

  // Always show first page
  pages.push(1)

  if (currentPage <= halfVisible + 2) {
    // Show pages from start
    for (let i = 2; i <= Math.min(maxVisible - 1, totalPages - 1); i++) {
      pages.push(i)
    }
    if (totalPages > maxVisible - 1) {
      pages.push("ellipsis")
    }
  } else if (currentPage >= totalPages - halfVisible - 1) {
    // Show pages from end
    if (totalPages > maxVisible - 1) {
      pages.push("ellipsis")
    }
    for (let i = Math.max(2, totalPages - maxVisible + 2); i <= totalPages - 1; i++) {
      pages.push(i)
    }
  } else {
    // Show pages around current
    pages.push("ellipsis")
    for (let i = currentPage - halfVisible + 1; i <= currentPage + halfVisible - 1; i++) {
      pages.push(i)
    }
    pages.push("ellipsis")
  }

  // Always show last page (if more than 1 page)
  if (totalPages > 1) {
    pages.push(totalPages)
  }

  return pages
}
