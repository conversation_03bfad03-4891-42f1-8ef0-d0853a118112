"""
Database Models for Vietnamese Web Novel Scraping API

This module defines the database schema and Pydantic models for the web novel
scraping and enhancement system using MongoDB with Motor (async MongoDB driver).
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, HttpUrl, field_validator, field_serializer
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic models"""

    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler):
        from pydantic_core import core_schema
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, v):
        if isinstance(v, ObjectId):
            return v
        if isinstance(v, str) and ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError("Invalid ObjectId")

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, handler):
        field_schema.update(type="string", format="objectid")
        return field_schema


class StoryStatus(str, Enum):
    """Story publication status"""
    ONGOING = "ongoing"
    COMPLETED = "completed"
    HIATUS = "hiatus"
    DROPPED = "dropped"
    UNKNOWN = "unknown"


class ScrapingStatus(str, Enum):
    """Scraping job status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class EnhancementStatus(str, Enum):
    """Enhancement job status"""
    NOT_ENHANCED = "not_enhanced"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    ENHANCED = "enhanced"
    FAILED = "failed"


# ============================================================================
# Core Data Models
# ============================================================================

class PageMetadata(BaseModel):
    """Page metadata and scraping information"""
    scraped_at: datetime = Field(default_factory=datetime.utcnow)
    scraping_duration: Optional[float] = None  # seconds
    page_number: int
    total_chapters_on_page: int = 0
    scraping_errors: List[str] = Field(default_factory=list)


class Page(BaseModel):
    """Page document model for Story → Page → Chapter hierarchy"""
    @field_serializer('page_url')
    def serialize_url(self, url: HttpUrl):
        return str(url)

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    story_id: PyObjectId
    page_number: int
    page_url: HttpUrl
    
    # Chapter information extracted from this page
    chapter_urls: List[str] = Field(default_factory=list)
    total_chapters_on_page: int = 0
    
    # Scraping status
    is_scraped: bool = False
    scraping_status: ScrapingStatus = ScrapingStatus.PENDING
    
    # Metadata
    metadata: Optional[PageMetadata] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

class ChapterMetadata(BaseModel):
    """Chapter metadata and scraping information"""
    @field_serializer('source_url')
    def serialize_url(self, url: HttpUrl):
        return str(url)

    word_count: Optional[int] = None
    character_count: Optional[int] = None
    scraped_at: datetime = Field(default_factory=datetime.utcnow)
    scraping_duration: Optional[float] = None  # seconds
    source_url: HttpUrl
    is_locked: bool = False
    scraping_errors: List[str] = Field(default_factory=list)


class EnhancementMetadata(BaseModel):
    """AI enhancement metadata and history"""
    enhanced_at: datetime = Field(default_factory=datetime.utcnow)
    enhancement_duration: Optional[float] = None  # seconds
    model_used: str = "gemini-2.5-flash"
    enhancement_version: str = "1.0"
    quality_score: Optional[float] = None  # 0-1 scale
    improvement_notes: List[str] = Field(default_factory=list)
    enhancement_errors: List[str] = Field(default_factory=list)
    
    # Content comparison metrics
    original_length: Optional[int] = None
    enhanced_length: Optional[int] = None
    similarity_score: Optional[float] = None  # 0-1 scale


class Chapter(BaseModel):
    """Chapter document model"""
    @field_serializer('url')
    def serialize_url(self, url: HttpUrl):
        return str(url)

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    story_id: PyObjectId
    page_id: PyObjectId  # New field for Page relationship
    chapter_number: int
    title: str
    url: HttpUrl
    
    # Content
    original_content: Optional[str] = None
    enhanced_content: Optional[str] = None
    
    # Status tracking
    is_scraped: bool = False
    is_enhanced: bool = False
    enhancement_status: EnhancementStatus = EnhancementStatus.NOT_ENHANCED
    
    # Metadata
    metadata: Optional[ChapterMetadata] = None
    enhancement_metadata: Optional[EnhancementMetadata] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class StoryMetadata(BaseModel):
    """Story metadata and scraping information"""
    @field_serializer('cover_image_url')
    def serialize_url(self, url: HttpUrl):
        return str(url) if url else None

    @field_validator("cover_image_url", mode='before')
    @classmethod
    def empty_str_to_none(cls, v):
        if v == "":
            return None
        return v

    author: Optional[str] = None
    description: Optional[str] = None
    genres: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    cover_image_url: Optional[HttpUrl] = None
    status: StoryStatus = StoryStatus.UNKNOWN
    total_chapters: Optional[int] = None
    total_pages: Optional[int] = None  # New field for total pages
    
    # Scraping metadata
    scraped_at: datetime = Field(default_factory=datetime.utcnow)
    scraping_duration: Optional[float] = None
    source_website: str = "webtruyen"
    scraping_errors: List[str] = Field(default_factory=list)


class Story(BaseModel):
    """Story document model"""
    @field_serializer('url')
    def serialize_url(self, url: HttpUrl):
        return str(url)

    @field_validator("url", mode='before')
    @classmethod
    def url_to_str(cls, v):
        if isinstance(v, HttpUrl):
            return str(v)
        return v

    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    title: str
    url: HttpUrl
    slug: Optional[str] = None  # URL-friendly identifier
    
    # Hierarchy tracking
    total_pages: int = 0  # Total pages containing chapters
    total_pages_scraped: int = 0  # Pages that have been scraped
    total_chapters_scraped: int = 0
    total_chapters_enhanced: int = 0
    
    # Status tracking
    scraping_status: ScrapingStatus = ScrapingStatus.PENDING
    enhancement_progress: float = 0.0  # 0-100 percentage
    
    # Metadata
    metadata: Optional[StoryMetadata] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_scraped_at: Optional[datetime] = None
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


# ============================================================================
# Job Management Models
# ============================================================================

class ScrapingJob(BaseModel):
    """Scraping job tracking"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    story_id: PyObjectId
    job_type: str  # "story_info", "chapters", "single_chapter"
    status: ScrapingStatus = ScrapingStatus.PENDING
    
    # Progress tracking
    total_items: Optional[int] = None
    completed_items: int = 0
    failed_items: int = 0
    progress_percentage: float = 0.0
    
    # Configuration
    chapter_urls: List[str] = Field(default_factory=list)
    max_concurrent: int = 3
    rate_limit_delay: float = 2.0
    
    # Results and errors
    results: Dict[str, Any] = Field(default_factory=dict)
    errors: List[str] = Field(default_factory=list)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


class EnhancementJob(BaseModel):
    """Enhancement job tracking"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    story_id: Optional[PyObjectId] = None
    chapter_ids: List[PyObjectId] = Field(default_factory=list)
    status: ScrapingStatus = ScrapingStatus.PENDING
    
    # Progress tracking
    total_chapters: int
    completed_chapters: int = 0
    failed_chapters: int = 0
    progress_percentage: float = 0.0
    
    # Configuration
    model_name: str = "gemini-2.5-flash"
    batch_size: int = 1  # Process one at a time to avoid rate limits
    
    # Results and errors
    results: Dict[str, Any] = Field(default_factory=dict)
    errors: List[str] = Field(default_factory=list)
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True


# ============================================================================
# Database Connection Management
# ============================================================================

class DatabaseManager:
    """MongoDB database connection and management"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.db_name = "webtruyen_api"
    
    @property
    def db(self):
        """Alias for database property for backward compatibility"""
        return self.database
    
    async def connect(self, connection_string: str = "mongodb://localhost:27017"):
        """Connect to MongoDB"""
        self.client = AsyncIOMotorClient(connection_string)
        self.database = self.client[self.db_name]
        
        # Test connection
        await self.client.admin.command('ping')
        print(f"✅ Connected to MongoDB database: {self.db_name}")
        
        # Create indexes
        await self._create_indexes()
    
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            print("✅ Disconnected from MongoDB")
    
    async def _create_indexes(self):
        """Create database indexes for optimal performance"""
        if self.database is None:
            return
        
        # Story indexes
        await self.database.stories.create_index("url", unique=True)
        await self.database.stories.create_index("slug")
        await self.database.stories.create_index("title")
        await self.database.stories.create_index("scraping_status")
        await self.database.stories.create_index("created_at")
        
        # Page indexes (new)
        await self.database.pages.create_index([("story_id", 1), ("page_number", 1)], unique=True)
        await self.database.pages.create_index("page_url")
        await self.database.pages.create_index("is_scraped")
        await self.database.pages.create_index("scraping_status")
        
        # Chapter indexes (updated with page_id)
        await self.database.chapters.create_index([("story_id", 1), ("chapter_number", 1)], unique=True)
        await self.database.chapters.create_index([("page_id", 1), ("chapter_number", 1)])
        await self.database.chapters.create_index("url", unique=True)
        await self.database.chapters.create_index("is_scraped")
        await self.database.chapters.create_index("is_enhanced")
        await self.database.chapters.create_index("enhancement_status")
        
        # Job indexes
        await self.database.scraping_jobs.create_index("story_id")
        await self.database.scraping_jobs.create_index("status")
        await self.database.scraping_jobs.create_index("created_at")
        
        await self.database.enhancement_jobs.create_index("story_id")
        await self.database.enhancement_jobs.create_index("status")
        await self.database.enhancement_jobs.create_index("created_at")
        
        print("✅ Database indexes created successfully")


# Global database manager instance
db_manager = DatabaseManager()
