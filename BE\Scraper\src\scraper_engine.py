# d:\Personal Projects\Vibe\Webtruyen\BE\Scraper\src\scraper_engine.py

import asyncio
import random
import sys
from typing import Dict, Any, Optional

from loguru import logger

from config_manager import ConfigManager
from anti_detection import AntiDetectionManager
from data_processor import DataProcessor
from browser_manager import BrowserManager
from scraping_strategy import ScrapingStrategy, WebtruyenStrategy

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

class ScraperEngine:
    def __init__(self, config_path: str = "config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        self.anti_detection = AntiDetectionManager(self.config_manager)
        self.data_processor = DataProcessor(self.config_manager)
        self.browser_manager: Optional[BrowserManager] = None
        self.strategy: Optional[ScrapingStrategy] = None
        self._setup_logging()

    def _setup_logging(self) -> None:
        log_config = self.config.get('logging', {})
        logger.add(
            log_config.get('file', 'scraper.log'),
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', '{time} | {level} | {message}'),
            rotation="10 MB"
        )

    def set_strategy_from_url(self, url: str) -> None:
        if "webtruyen" in url:
            target_config = self.config_manager.get_target_config('webtruyen')
            self.strategy = WebtruyenStrategy(target_config)
        else:
            raise ValueError(f"Unsupported website URL: {url}")

    async def start(self) -> None:
        self.browser_manager = BrowserManager(self.config)
        await self.browser_manager.start()

    async def close(self) -> None:
        if self.browser_manager:
            await self.browser_manager.stop()

    async def scrape_story_info(self, url: str) -> Dict[str, Any]:
        if not self.strategy:
            self.set_strategy_from_url(url)

        page = await self.browser_manager.new_page()

        try:
            logger.info(f"🚀 Scraping story info from: {url}")
            logger.info(f"📄 Navigating to page...")
            await page.goto(url, wait_until='networkidle')
            logger.info(f"✅ Page loaded successfully")
            
            # Save HTML structure to debug folder
            import os
            from urllib.parse import urlparse
            html_content = await page.content()
            
            # Create debug filename from URL
            parsed_url = urlparse(url)
            filename = parsed_url.path.replace('/', '_').replace('-', '_')
            if not filename or filename == '_':
                filename = 'story_page'
            
            # Use absolute path for html_debug directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            scraper_dir = os.path.dirname(current_dir)  # Go up one level from src
            debug_dir = os.path.join(scraper_dir, 'html_debug')
            debug_file = os.path.join(debug_dir, f"{filename}.html")
            
            # Ensure html_debug directory exists
            os.makedirs(debug_dir, exist_ok=True)
            
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"💾 HTML saved to: {debug_file}")
            
            logger.info(f"🔍 Extracting story info using strategy...")
            story_info = await self.strategy.extract_story_info(page, url)
            logger.info(f"📊 Story info extracted: {story_info.get('title', 'No title')}")
            
            # Log extracted data for debugging
            logger.info(f"📖 Title: {story_info.get('title', 'Not found')}")
            logger.info(f"👤 Author: {story_info.get('metadata', {}).get('author', 'Not found')}")
            logger.info(f"📝 Description: {story_info.get('metadata', {}).get('description', 'Not found')[:100]}...")
            
            # Add data to processor cache for later export if needed
            self.data_processor.add_data(story_info)
            return story_info
        except Exception as e:
            logger.error(f"❌ Failed to scrape story info from {url}: {e}")
            logger.error(f"🔍 Exception type: {type(e).__name__}")
            logger.error(f"📋 Exception details: {str(e)}")
            import traceback
            logger.error(f"📚 Full traceback: {traceback.format_exc()}")
            return {'error': str(e), 'url': url}
        finally:
            await page.close()

    async def batch_scrape_chapters(self, url: str, max_pages: Optional[int] = None) -> Dict[str, Any]:
        if not self.strategy:
            self.set_strategy_from_url(url)

        page = await self.browser_manager.new_page()
        try:
            logger.info(f"🚀 Batch scraping chapters from: {url}")
            total_pages = await self.strategy.determine_total_pages(page)
            if max_pages:
                total_pages = min(total_pages, max_pages)

            all_chapters = []
            for page_num in range(1, total_pages + 1):
                page_url = self._construct_page_url(url, page_num)
                logger.info(f"📄 Processing page {page_num}/{total_pages}: {page_url}")
                await page.goto(page_url, wait_until='networkidle')
                chapters = await self.strategy.collect_chapters_from_page(page, page_num)
                all_chapters.extend(chapters)
                await asyncio.sleep(random.uniform(1, 3))

            chapter_data = {'chapters': all_chapters, 'total_chapters': len(all_chapters)}
            story_title = url.split('/')[-1].replace('.html', '')
            self.data_processor.add_data(chapter_data)
            logger.info(f"🎉 Found {len(all_chapters)} total chapters.")
            return chapter_data
        except Exception as e:
            logger.error(f"❌ Failed to batch scrape chapters from {url}: {e}")
            return {'error': str(e), 'url': url}
        finally:
            await page.close()

    def _construct_page_url(self, base_url: str, page_number: int) -> str:
        if page_number == 1:
            return base_url
        else:
            separator = '&' if '?' in base_url else '?'
            return f"{base_url}{separator}trang={page_number}#chapter-list"

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
