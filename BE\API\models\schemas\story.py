"""
Story related API Models
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl

from ..database import StoryStatus, ScrapingStatus
from .common import APIResponse, PaginatedResponse

# ============================================================================
# Story Data Retrieval API Models
# ============================================================================

class StoryListRequest(BaseModel):
    """Request for story list with filtering"""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    search: Optional[str] = None
    author: Optional[str] = None
    status: Optional[StoryStatus] = None
    genre: Optional[str] = None
    scraping_status: Optional[ScrapingStatus] = None
    sort_by: str = Field(default="created_at", pattern="^(created_at|updated_at|title|total_chapters)$")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$")


class StoryListItem(BaseModel):
    """Story item in list response"""
    id: str
    title: str
    author: Optional[str] = None
    status: StoryStatus
    total_chapters: Optional[int] = None
    total_chapters_scraped: int
    total_chapters_enhanced: int
    enhancement_progress: float
    cover_image_url: Optional[HttpUrl] = None
    created_at: datetime
    updated_at: datetime


class StoryListResponse(PaginatedResponse):
    """Response for story list"""
    data: List[StoryListItem]


class StoryDetailsResponse(APIResponse):
    """Response for story details"""
    id: str
    title: str
    url: HttpUrl
    author: Optional[str] = None
    description: Optional[str] = None
    genres: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    status: StoryStatus
    cover_image_url: Optional[HttpUrl] = None
    total_chapters: Optional[int] = None
    total_chapters_scraped: int
    total_chapters_enhanced: int
    enhancement_progress: float
    scraping_status: ScrapingStatus
    created_at: datetime
    updated_at: datetime
    last_scraped_at: Optional[datetime] = None