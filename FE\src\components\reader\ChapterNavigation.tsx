'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Chapter } from '@/types/story';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface ChapterNavigationProps {
  storyId: string;
  previousChapter: Chapter | null;
  nextChapter: Chapter | null;
}

const ChapterNavigation = ({
  storyId,
  previousChapter,
  nextChapter,
}: ChapterNavigationProps) => {
  const router = useRouter();

  return (
    <div className="">
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        {/* Previous Chapter Button */}
        <div className="flex-1 min-w-fit">
          <Button
            onClick={() => previousChapter && router.push(`/stories/${storyId}/${previousChapter.chapter_number}`)}
            disabled={!previousChapter}
            variant={previousChapter ? "default" : "secondary"}
            className="inline-flex items-center"
          >
            <ChevronLeft className="w-4 h-4 mr-2" />
            Chương trước
          </Button>
        </div>

        {/* Next Chapter Button */}
        <div className="flex-1 flex justify-end">
          <Button
            onClick={() => nextChapter && router.push(`/stories/${storyId}/${nextChapter.chapter_number}`)}
            disabled={!nextChapter}
            variant={nextChapter ? "link" : "secondary"}
            className="inline-flex items-center"
          >
            Chương sau
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChapterNavigation;