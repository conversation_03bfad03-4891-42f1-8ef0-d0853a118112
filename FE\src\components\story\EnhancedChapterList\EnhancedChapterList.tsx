'use client';

import React, { useState, useEffect } from 'react';
import { Chapter } from '@/types/story';
import { useChapterManagement } from '@/hooks/useChapterManagement/useChapterManagement';
import { useBatchActions } from '@/hooks/useBatchActions/useBatchActions';
import { ChapterListHeader } from './ChapterListHeader';
import { BatchActionsPanel } from './BatchActionsPanel';
import { ChapterList } from './ChapterList';
import Pagination from '@/components/common/Pagination';
import { ToastContainer, useToast } from '@/components/ui/Toast';

interface EnhancedChapterListProps {
  storyId: string;
  chapters?: Chapter[];
}

const EnhancedChapterList: React.FC<EnhancedChapterListProps> = ({ storyId, chapters: propChapters = [] }) => {
  const [selectionMode, setSelectionMode] = useState<'scraping' | 'enhancement' | null>(null);
  const { toasts, removeToast } = useToast();

  const {
    paginatedChapters,
    chaptersLoading,
    filter,
    setFilter,
    currentPage,
    setCurrentPage,
    totalPages,
    chapterStats,
    selectedChapters,
    setSelectedChapters,
    handleSelectChapter,
    handleSelectAll,
    handleDeselectAll,
  } = useChapterManagement(storyId, propChapters);

  const { 
    batchScrapingState, 
    batchEnhancementState, 
    handleBatchScrape, 
    handleBatchEnhance, 
    cancelAction
  } = useBatchActions(() => {
    setSelectionMode(null);
    setSelectedChapters(new Set());
  });

  const handleStartAction = () => {
    const chapterIds = Array.from(selectedChapters);
    if (selectionMode === 'scraping') {
      handleBatchScrape(storyId, chapterIds);
    } else if (selectionMode === 'enhancement') {
      handleBatchEnhance(storyId, chapterIds);
    }
  };

  const handleCancelAction = () => {
    const jobId = selectionMode === 'scraping' ? batchScrapingState.jobId : batchEnhancementState.jobId;
    if (jobId) {
      cancelAction(jobId);
    }
  };

  useEffect(() => {
    setSelectedChapters(new Set());
  }, [selectionMode, setSelectedChapters]);

  const isProcessing = batchScrapingState.isActive || batchEnhancementState.isActive;
  const currentProgress = selectionMode === 'scraping' 
    ? batchScrapingState.progress
    : batchEnhancementState.progress;

  const progressPercentage = currentProgress?.progress_percentage ?? 0;

  return (
    <div className="space-y-4">
      <ToastContainer toasts={toasts} onDismiss={removeToast} />
      <ChapterListHeader
        stats={chapterStats}
        selectionMode={selectionMode}
        setSelectionMode={setSelectionMode}
        filter={filter}
        onFilterChange={setFilter}
      />

      {selectionMode && (
        <BatchActionsPanel
          selectionMode={selectionMode}
          isProcessing={isProcessing}
          progress={progressPercentage}
          onStartAction={handleStartAction}
          onCancel={handleCancelAction}
          selectedCount={selectedChapters.size}
        />
      )}

      <ChapterList
        storyId={storyId}
        chapters={paginatedChapters}
        loading={chaptersLoading}
        selectionMode={selectionMode}
        selectedChapters={selectedChapters}
        onSelectChapter={handleSelectChapter}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
      />

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default EnhancedChapterList;