"""
Error Handling Middleware

This middleware provides comprehensive error handling, logging, and
standardized error responses for the API.
"""

import logging
import traceback
from datetime import datetime
from typing import Callable

from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from API.models.schemas import APIResponse


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for centralized error handling and logging"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = logging.getLogger("api.errors")
    
    async def dispatch(self, request: Request, call_next: Callable):
        """Process request with error handling"""
        try:
            response = await call_next(request)
            return response
            
        except HTTPException as e:
            # Handle FastAPI HTTP exceptions
            return await self._handle_http_exception(request, e)
            
        except ValidationError as e:
            # Handle Pydantic validation errors
            return await self._handle_validation_error(request, e)
            
        except DatabaseError as e:
            # Handle database-related errors
            return await self._handle_database_error(request, e)
            
        except ScrapingError as e:
            # Handle scraping-related errors
            return await self._handle_scraping_error(request, e)
            
        except AIEnhancementError as e:
            # Handle AI enhancement errors
            return await self._handle_ai_error(request, e)
            
        except Exception as e:
            # Handle unexpected errors
            return await self._handle_unexpected_error(request, e)
    
    async def _handle_http_exception(self, request: Request, exc: HTTPException):
        """Handle FastAPI HTTP exceptions"""
        self.logger.warning(
            f"HTTP Exception: {exc.status_code} - {exc.detail} - "
            f"Path: {request.url.path} - Method: {request.method}"
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "message": exc.detail,
                "error_code": f"HTTP_{exc.status_code}",
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_validation_error(self, request: Request, exc):
        """Handle Pydantic validation errors"""
        self.logger.warning(
            f"Validation Error: {str(exc)} - "
            f"Path: {request.url.path} - Method: {request.method}"
        )
        
        return JSONResponse(
            status_code=422,
            content={
                "success": False,
                "message": "Validation error",
                "error_code": "VALIDATION_ERROR",
                "details": str(exc),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_database_error(self, request: Request, exc):
        """Handle database-related errors"""
        self.logger.error(
            f"Database Error: {str(exc)} - "
            f"Path: {request.url.path} - Method: {request.method}",
            exc_info=True
        )
        
        # Don't expose internal database errors to clients
        return JSONResponse(
            status_code=503,
            content={
                "success": False,
                "message": "Database service temporarily unavailable",
                "error_code": "DATABASE_ERROR",
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_scraping_error(self, request: Request, exc):
        """Handle scraping-related errors"""
        self.logger.error(
            f"Scraping Error: {str(exc)} - "
            f"Path: {request.url.path} - Method: {request.method}",
            exc_info=True
        )
        
        return JSONResponse(
            status_code=502,
            content={
                "success": False,
                "message": "Scraping service error",
                "error_code": "SCRAPING_ERROR",
                "details": str(exc),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_ai_error(self, request: Request, exc):
        """Handle AI enhancement errors"""
        self.logger.error(
            f"AI Enhancement Error: {str(exc)} - "
            f"Path: {request.url.path} - Method: {request.method}",
            exc_info=True
        )
        
        return JSONResponse(
            status_code=502,
            content={
                "success": False,
                "message": "AI enhancement service error",
                "error_code": "AI_ENHANCEMENT_ERROR",
                "details": str(exc),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    async def _handle_unexpected_error(self, request: Request, exc: Exception):
        """Handle unexpected errors"""
        # Log full traceback for debugging
        self.logger.error(
            f"Unexpected Error: {str(exc)} - "
            f"Path: {request.url.path} - Method: {request.method}\n"
            f"Traceback: {traceback.format_exc()}"
        )
        
        return JSONResponse(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "message": "Internal server error",
                "error_code": "INTERNAL_SERVER_ERROR",
                "timestamp": datetime.utcnow().isoformat()
            }
        )


# ============================================================================
# Custom Exception Classes
# ============================================================================

class APIException(Exception):
    """Base exception for API errors"""
    
    def __init__(self, message: str, error_code: str = None, status_code: int = 500):
        self.message = message
        self.error_code = error_code or "API_ERROR"
        self.status_code = status_code
        super().__init__(self.message)


class ValidationError(APIException):
    """Exception for validation errors"""
    
    def __init__(self, message: str, field: str = None):
        self.field = field
        super().__init__(message, "VALIDATION_ERROR", 422)


class DatabaseError(APIException):
    """Exception for database errors"""
    
    def __init__(self, message: str, operation: str = None):
        self.operation = operation
        super().__init__(message, "DATABASE_ERROR", 503)


class ScrapingError(APIException):
    """Exception for scraping errors"""
    
    def __init__(self, message: str, url: str = None, error_type: str = None):
        self.url = url
        self.error_type = error_type
        super().__init__(message, "SCRAPING_ERROR", 502)


class AIEnhancementError(APIException):
    """Exception for AI enhancement errors"""
    
    def __init__(self, message: str, chapter_id: str = None, model: str = None):
        self.chapter_id = chapter_id
        self.model = model
        super().__init__(message, "AI_ENHANCEMENT_ERROR", 502)


class RateLimitError(APIException):
    """Exception for rate limiting errors"""
    
    def __init__(self, message: str, retry_after: int = 60):
        self.retry_after = retry_after
        super().__init__(message, "RATE_LIMIT_ERROR", 429)


class JobError(APIException):
    """Exception for job processing errors"""
    
    def __init__(self, message: str, job_id: str = None, job_type: str = None):
        self.job_id = job_id
        self.job_type = job_type
        super().__init__(message, "JOB_ERROR", 500)


# ============================================================================
# Error Response Utilities
# ============================================================================

def create_error_response(
    message: str,
    error_code: str = "ERROR",
    status_code: int = 500,
    details: dict = None
) -> JSONResponse:
    """Create standardized error response"""
    content = {
        "success": False,
        "message": message,
        "error_code": error_code,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    if details:
        content["details"] = details
    
    return JSONResponse(
        status_code=status_code,
        content=content
    )


def create_validation_error_response(errors: list) -> JSONResponse:
    """Create validation error response"""
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation failed",
            "error_code": "VALIDATION_ERROR",
            "errors": errors,
            "timestamp": datetime.utcnow().isoformat()
        }
    )


def create_not_found_response(resource: str, identifier: str = None) -> JSONResponse:
    """Create not found error response"""
    message = f"{resource} not found"
    if identifier:
        message += f": {identifier}"
    
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": message,
            "error_code": "NOT_FOUND",
            "timestamp": datetime.utcnow().isoformat()
        }
    )
