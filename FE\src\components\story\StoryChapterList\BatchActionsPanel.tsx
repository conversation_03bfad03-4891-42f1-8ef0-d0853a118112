'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Loader2 } from 'lucide-react';

interface BatchActionsPanelProps {
  selectionMode: 'scraping' | 'enhancement';
  selectedCount: number;
  isProcessing: boolean;
  progress: number;
  onStartAction: () => void;
  onCancel: () => void;
}

export const BatchActionsPanel: React.FC<BatchActionsPanelProps> = ({ 
  selectionMode, 
  selectedCount, 
  isProcessing, 
  progress, 
  onStartAction, 
  onCancel 
}) => {
  const actionText = selectionMode === 'scraping' ? 'Scrape' : 'Enhance';

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 z-50">
      <h3 className="font-bold text-lg mb-2">Batch {actionText}</h3>
      <p className="text-sm text-gray-500 mb-4">{selectedCount} chapters selected.</p>
      
      {isProcessing && (
        <div className="mb-4">
          <Progress value={progress} className="w-full" />
          <p className="text-xs text-center mt-1">{Math.round(progress)}% complete</p>
        </div>
      )}

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel} disabled={isProcessing}>Cancel</Button>
        <Button onClick={onStartAction} disabled={isProcessing || selectedCount === 0}>
          {isProcessing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
          {isProcessing ? `${actionText}ing...` : `Start ${actionText}`}
        </Button>
      </div>
    </div>
  );
};
