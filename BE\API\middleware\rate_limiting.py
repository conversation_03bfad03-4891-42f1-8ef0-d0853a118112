"""
Rate Limiting Middleware

This middleware implements rate limiting for API endpoints to prevent abuse
and ensure fair usage of scraping and AI enhancement resources.
"""

import time
import asyncio
from typing import Dict, Optional, Callable
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from API.core.config import get_settings


class TokenBucket:
    """Token bucket algorithm implementation for rate limiting"""
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket
        
        Args:
            capacity: Maximum number of tokens
            refill_rate: Tokens added per second
        """
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """
        Try to consume tokens from bucket
        
        Args:
            tokens: Number of tokens to consume
            
        Returns:
            True if tokens were consumed, False otherwise
        """
        self._refill()
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False
    
    def _refill(self):
        """Refill tokens based on time elapsed"""
        now = time.time()
        elapsed = now - self.last_refill
        
        # Add tokens based on elapsed time
        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now
    
    def time_until_available(self, tokens: int = 1) -> float:
        """
        Calculate time until requested tokens are available
        
        Args:
            tokens: Number of tokens needed
            
        Returns:
            Time in seconds until tokens are available
        """
        self._refill()
        
        if self.tokens >= tokens:
            return 0.0
        
        tokens_needed = tokens - self.tokens
        return tokens_needed / self.refill_rate


class SlidingWindowCounter:
    """Sliding window counter for rate limiting"""
    
    def __init__(self, window_size: int, max_requests: int):
        """
        Initialize sliding window counter
        
        Args:
            window_size: Window size in seconds
            max_requests: Maximum requests per window
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.requests = deque()
    
    def is_allowed(self) -> bool:
        """Check if request is allowed within rate limit"""
        now = time.time()
        
        # Remove old requests outside the window
        while self.requests and self.requests[0] <= now - self.window_size:
            self.requests.popleft()
        
        # Check if we're within the limit
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        
        return False
    
    def time_until_reset(self) -> float:
        """Calculate time until window resets"""
        if not self.requests:
            return 0.0
        
        oldest_request = self.requests[0]
        return max(0.0, self.window_size - (time.time() - oldest_request))


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware with different strategies per endpoint"""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        
        # Storage for rate limiters per client
        self.client_buckets: Dict[str, TokenBucket] = {}
        self.client_windows: Dict[str, SlidingWindowCounter] = {}
        
        # Endpoint-specific rate limits
        self.endpoint_limits = {
            "/api/v1/scraping/story-info": {
                "requests_per_minute": 10,
                "burst_limit": 3
            },
            "/api/v1/scraping/batch-chapters": {
                "requests_per_minute": 5,
                "burst_limit": 2
            },
            "/api/v1/enhancement/batch": {
                "requests_per_minute": 3,
                "burst_limit": 1
            },
            "default": {
                "requests_per_minute": 60,
                "burst_limit": 10
            }
        }
        
        # Cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
    
    async def dispatch(self, request: Request, call_next: Callable):
        """Process request with rate limiting"""
        client_id = self._get_client_id(request)
        endpoint = self._get_endpoint_key(request)
        
        # Check rate limits
        if not await self._check_rate_limit(client_id, endpoint):
            return await self._rate_limit_response(client_id, endpoint)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        self._add_rate_limit_headers(response, client_id, endpoint)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting"""
        # Try to get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Check for forwarded headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        # Could be extended to use API keys or user authentication
        return f"ip:{client_ip}"
    
    def _get_endpoint_key(self, request: Request) -> str:
        """Get endpoint key for rate limiting"""
        path = request.url.path
        
        # Check for specific endpoint limits
        for endpoint_pattern in self.endpoint_limits:
            if endpoint_pattern != "default" and path.startswith(endpoint_pattern):
                return endpoint_pattern
        
        return "default"
    
    async def _check_rate_limit(self, client_id: str, endpoint: str) -> bool:
        """Check if request is within rate limits"""
        limits = self.endpoint_limits.get(endpoint, self.endpoint_limits["default"])
        
        # Get or create token bucket for burst limiting
        bucket_key = f"{client_id}:{endpoint}:bucket"
        if bucket_key not in self.client_buckets:
            self.client_buckets[bucket_key] = TokenBucket(
                capacity=limits["burst_limit"],
                refill_rate=limits["requests_per_minute"] / 60.0
            )
        
        # Get or create sliding window for rate limiting
        window_key = f"{client_id}:{endpoint}:window"
        if window_key not in self.client_windows:
            self.client_windows[window_key] = SlidingWindowCounter(
                window_size=60,  # 1 minute window
                max_requests=limits["requests_per_minute"]
            )
        
        # Check both limits
        bucket = self.client_buckets[bucket_key]
        window = self.client_windows[window_key]
        
        return bucket.consume() and window.is_allowed()
    
    async def _rate_limit_response(self, client_id: str, endpoint: str) -> JSONResponse:
        """Return rate limit exceeded response"""
        limits = self.endpoint_limits.get(endpoint, self.endpoint_limits["default"])
        
        # Calculate retry after time
        bucket_key = f"{client_id}:{endpoint}:bucket"
        window_key = f"{client_id}:{endpoint}:window"
        
        retry_after = 60  # Default 1 minute
        
        if bucket_key in self.client_buckets:
            bucket_retry = self.client_buckets[bucket_key].time_until_available()
            retry_after = min(retry_after, bucket_retry)
        
        if window_key in self.client_windows:
            window_retry = self.client_windows[window_key].time_until_reset()
            retry_after = max(retry_after, window_retry)
        
        return JSONResponse(
            status_code=429,
            content={
                "success": False,
                "message": "Rate limit exceeded",
                "error_code": "RATE_LIMIT_EXCEEDED",
                "limits": {
                    "requests_per_minute": limits["requests_per_minute"],
                    "burst_limit": limits["burst_limit"]
                },
                "retry_after": int(retry_after),
                "timestamp": datetime.utcnow().isoformat()
            },
            headers={
                "Retry-After": str(int(retry_after)),
                "X-RateLimit-Limit": str(limits["requests_per_minute"]),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(time.time() + retry_after))
            }
        )
    
    def _add_rate_limit_headers(self, response, client_id: str, endpoint: str):
        """Add rate limit headers to response"""
        limits = self.endpoint_limits.get(endpoint, self.endpoint_limits["default"])
        
        # Get current bucket state
        bucket_key = f"{client_id}:{endpoint}:bucket"
        if bucket_key in self.client_buckets:
            bucket = self.client_buckets[bucket_key]
            bucket._refill()  # Update tokens
            remaining = int(bucket.tokens)
        else:
            remaining = limits["burst_limit"]
        
        # Add headers
        response.headers["X-RateLimit-Limit"] = str(limits["requests_per_minute"])
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time() + 60))
    
    def _start_cleanup_task(self):
        """Start background cleanup task"""
        async def cleanup():
            while True:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                await self._cleanup_old_entries()
        
        self._cleanup_task = asyncio.create_task(cleanup())
    
    async def _cleanup_old_entries(self):
        """Clean up old rate limiting entries"""
        current_time = time.time()
        
        # Clean up token buckets (remove inactive ones)
        inactive_buckets = []
        for key, bucket in self.client_buckets.items():
            if current_time - bucket.last_refill > 3600:  # 1 hour inactive
                inactive_buckets.append(key)
        
        for key in inactive_buckets:
            del self.client_buckets[key]
        
        # Clean up sliding windows (they clean themselves)
        inactive_windows = []
        for key, window in self.client_windows.items():
            if not window.requests or current_time - window.requests[-1] > 3600:
                inactive_windows.append(key)
        
        for key in inactive_windows:
            del self.client_windows[key]
