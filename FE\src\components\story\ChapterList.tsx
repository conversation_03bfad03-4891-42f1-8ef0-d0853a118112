'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Chapter } from '@/types/story';
import { useChaptersCache } from '@/hooks/useChaptersCache';
import Pagination from '@/components/common/Pagination';

interface ChapterListProps {
  storyId: string;
}

const ChapterList = ({ storyId }: ChapterListProps) => {
  const { 
    chapters: cachedChapters, 
    loading: cacheLoading, 
    totalChapters: cacheTotalChapters,
    currentPage,
    totalPages,
    loadChapters 
  } = useChaptersCache(storyId);
  
  // Load initial chapters on mount
  useEffect(() => {
    loadChapters(1, 50);
  }, [storyId, loadChapters]);

  // Handle page change
  const handlePageChange = (page: number) => {
    loadChapters(page, 50);
  };

  return (
    <div className="bg-zinc-800/50 rounded-lg p-6 border border-zinc-700/50">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white"><PERSON><PERSON> sách chương</h2>
        <span className="text-gray-400">{cacheTotalChapters} chương</span>
      </div>

      {cacheLoading && cachedChapters.length === 0 ? (
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-zinc-700/50 rounded-lg p-4">
                <div className="h-4 bg-zinc-600 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-zinc-600 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : cachedChapters.length === 0 ? (
        <div className="text-center py-8">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
          </svg>
          <p className="text-gray-400">Chưa có chương nào được tải</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {cachedChapters.map((chapter) => (
              <Link
                 key={chapter.id}
                href={`/stories/${storyId}/${chapter.chapter_number}`}
                className="group block bg-zinc-700/50 hover:bg-zinc-700 rounded-lg p-4 transition-all duration-200 border border-zinc-600/50 hover:border-indigo-500/50"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-white group-hover:text-indigo-400 transition-colors truncate">
                      Chương {chapter.chapter_number}
                    </h3>
                    {chapter.title && (
                      <p className="text-sm text-gray-400 mt-1 truncate">
                        {chapter.title}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                      <span>{(chapter.word_count || 0).toLocaleString()} từ</span>
                      <span>{new Date(chapter.updated_at).toLocaleDateString('vi-VN')}</span>
                    </div>
                    <div className="flex gap-2 mt-2">
                      {chapter.is_enhanced ? (
                        <span className="inline-flex items-center gap-1 text-xs text-blue-100 bg-blue-500/80 px-2 py-1 rounded-full font-medium">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          Đã nâng cấp
                        </span>
                      ) : chapter.is_scraped ? (
                        <span className="inline-flex items-center gap-1 text-xs text-green-100 bg-green-500/80 px-2 py-1 rounded-full font-medium">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          Đã cào
                        </span>
                      ) : (
                        <span className="inline-flex items-center gap-1 text-xs text-gray-300 bg-gray-500/60 px-2 py-1 rounded-full font-medium">
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                          Chưa cào
                        </span>
                      )}
                    </div>
                  </div>
                  <svg 
                    className="w-5 h-5 text-gray-400 group-hover:text-indigo-400 transition-colors ml-2" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </Link>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ChapterList;