"""
Export related API Models
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

from .common import APIResponse

# ============================================================================
# Export API Models
# ============================================================================

class ExportRequest(BaseModel):
    """Request to export story/chapter data"""
    story_ids: Optional[List[str]] = None
    chapter_ids: Optional[List[str]] = None
    export_format: str = Field(default="json", pattern="^(json|csv|epub|txt)$")
    include_enhanced: bool = True
    include_original: bool = False
    include_metadata: bool = True


class ExportResponse(APIResponse):
    """Response for export request"""
    export_id: str
    download_url: str
    file_size: Optional[int] = None
    expires_at: datetime