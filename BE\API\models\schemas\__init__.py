"""
Schemas for API Models
"""

from .common import APIResponse, PaginationInfo, PaginatedResponse
from .story import StoryListRequest, StoryListItem, StoryListResponse, StoryDetailsResponse
from .chapter import ChapterListRequest, ChapterListItem, ChapterListResponse, ChapterContentResponse
from .scraping import (
    StoryInfoRequest, ChapterInfo, PageInfo, StoryInfoResponse,
    BatchChapterScrapeRequest, ChapterContent, BatchChapterScrapeResponse
)
from .enhancement import (
    ChapterEnhancementRequest, BatchEnhancementRequest, EnhancementResult, BatchEnhancementResponse
)
from .job import JobStatusResponse
from .search import SearchRequest, ContentComparisonRequest, ContentComparisonResponse
from .export import ExportRequest, ExportResponse

__all__ = [
    # Common
    "APIResponse",
    "PaginationInfo",
    "PaginatedResponse",
    # Story
    "StoryListRequest",
    "StoryListItem",
    "StoryListResponse",
    "StoryDetailsResponse",
    # Chapter
    "ChapterListRequest",
    "ChapterListItem",
    "ChapterListResponse",
    "ChapterContentResponse",
    # Scraping
    "StoryInfoRequest",
    "ChapterInfo",
    "PageInfo",
    "StoryInfoResponse",
    "BatchChapterScrapeRequest",
    "ChapterContent",
    "BatchChapterScrapeResponse",
    # Enhancement
    "ChapterEnhancementRequest",
    "BatchEnhancementRequest",
    "EnhancementResult",
    "BatchEnhancementResponse",
    # Job
    "JobStatusResponse",
    # Search
    "SearchRequest",
    "ContentComparisonRequest",
    "ContentComparisonResponse",
    # Export
    "ExportRequest",
    "ExportResponse",
]