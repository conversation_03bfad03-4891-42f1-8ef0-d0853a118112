# d:\Personal Projects\Vibe\Webtruyen\BE\Scraper\src\browser_manager.py

import asyncio
import sys
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Playwright

class BrowserManager:
    """Manages the Playwright browser instance."""
    def __init__(self, config: dict):
        self.config = config
        self.playwright: Playwright = None
        self.browser: Browser = None

    async def __aenter__(self):
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.stop()

    async def start(self):
        """Starts the browser and applies configurations."""
        # Fix for Windows asyncio subprocess issue
        # Note: Don't change event loop policy when already running in an event loop
        # This can cause issues in FastAPI/Uvicorn environments
        if sys.platform == 'win32':
            try:
                # Only set event loop policy if we're not already in an event loop
                current_loop = asyncio.get_running_loop()
                # If we get here, we're already in a running loop, don't change policy
            except RuntimeError:
                # No running loop, safe to set policy
                if isinstance(asyncio.get_event_loop(), asyncio.SelectorEventLoop):
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        self.playwright = await async_playwright().start()
        browser_config = self.config.get('scraper', {}).get('browser', {})
        launch_options = {
            'headless': browser_config.get('headless', True),
            'args': browser_config.get('args', [])
        }
        
        # Check if custom executable path is specified and exists
        executable_path = browser_config.get('executable_path')
        if executable_path:
            import os
            if os.path.exists(executable_path):
                launch_options['executable_path'] = executable_path
        
        from loguru import logger
        logger.info(f"Launching browser with options: {launch_options}")
        self.browser = await self.playwright.chromium.launch(**launch_options)
        logger.info("Browser launched successfully.")

    async def stop(self):
        """Stops the browser and the Playwright instance."""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def new_page(self) -> Page:
        """Creates a new page in the browser."""
        if not self.browser:
            raise RuntimeError("Browser is not started.")
        return await self.browser.new_page()