# Image Optimization Guide

## Overview

This guide explains the global image optimization solution implemented for the Webtruyen application. The solution addresses common Next.js image issues and provides a robust, reusable component for handling external images.

## Problem Solved

The original issue was related to Next.js Image component requiring external domains to be configured. The error "hostname is not configured under images in your next.config.js" occurs when trying to load images from external sources without proper configuration.

## Solution Components

### 1. Next.js Configuration (`next.config.mjs`)

```javascript
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'static.cuatui.us',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'webtruyen.diendantruyen.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**', // Allow all HTTPS domains (use with caution)
      },
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
};
```

**Features:**
- Configures allowed external domains
- Enables modern image formats (WebP, AVIF)
- Optimizes device and image sizes
- Allows SVG with security policies

### 2. OptimizedImage Component (`src/components/ui/OptimizedImage.tsx`)

A wrapper around Next.js Image component that provides:

**Features:**
- **Error Handling**: Graceful fallbacks when images fail to load
- **Loading States**: Shows loading spinner while images load
- **Null Safety**: Handles null/undefined src values
- **Fallback Images**: Configurable fallback images
- **Placeholder Support**: Built-in placeholder when no image available
- **TypeScript Support**: Fully typed with proper interfaces

**Props:**
```typescript
interface OptimizedImageProps {
  src: string | null | undefined;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  fallbackSrc?: string;
  showFallback?: boolean;
  onError?: () => void;
}
```

### 3. Utility Functions (`src/lib/utils.ts`)

**cn() function**: Merges Tailwind CSS classes intelligently
**generateBlurDataURL()**: Creates placeholder blur data URLs
**formatFileSize()**: Formats file sizes for display
**debounce()**: Debounces function calls

## Usage Examples

### Basic Usage

```tsx
import OptimizedImage from '@/components/ui/OptimizedImage';

<OptimizedImage
  src={story.cover_image_url}
  alt={story.title}
  fill
  className="object-cover"
  sizes="(max-width: 768px) 100vw, 50vw"
/>
```

### With Custom Fallback

```tsx
<OptimizedImage
  src={user.avatar}
  alt={user.name}
  width={100}
  height={100}
  fallbackSrc="/images/default-avatar.png"
  className="rounded-full"
/>
```

### With Loading Priority

```tsx
<OptimizedImage
  src={heroImage}
  alt="Hero image"
  fill
  priority={true}
  quality={90}
  className="object-cover"
/>
```

## Best Practices

### 1. Image Sizing
- Always provide `sizes` prop for responsive images
- Use `fill` for container-based sizing
- Specify `width` and `height` for fixed-size images

### 2. Performance
- Set `priority={true}` for above-the-fold images
- Use appropriate `quality` settings (75-85 for most cases)
- Leverage modern formats (WebP, AVIF) through config

### 3. Accessibility
- Always provide meaningful `alt` text
- Use empty `alt=""` for decorative images

### 4. Error Handling
- Provide fallback images for critical UI elements
- Use `onError` callback for custom error handling
- Consider `showFallback={false}` for optional images

## Security Considerations

### Remote Patterns
- Be specific with hostname patterns when possible
- Avoid using `hostname: '**'` in production
- Regularly audit allowed domains

### Content Security Policy
- SVG support includes CSP for security
- Review and adjust CSP as needed

## Migration Guide

### From Next.js Image to OptimizedImage

**Before:**
```tsx
import Image from 'next/image';

{story.cover_image_url ? (
  <Image
    src={story.cover_image_url}
    alt={story.title}
    fill
    className="object-cover"
  />
) : (
  <div className="placeholder">No image</div>
)}
```

**After:**
```tsx
import OptimizedImage from '@/components/ui/OptimizedImage';

<OptimizedImage
  src={story.cover_image_url}
  alt={story.title}
  fill
  className="object-cover"
/>
```

## Troubleshooting

### Common Issues

1. **"hostname is not configured" error**
   - Add the domain to `remotePatterns` in `next.config.mjs`
   - Restart the development server after config changes

2. **Images not loading**
   - Check network connectivity
   - Verify image URLs are accessible
   - Check browser console for CORS errors

3. **Poor performance**
   - Optimize `sizes` prop for your layout
   - Use appropriate `quality` settings
   - Enable priority loading for critical images

### Debug Mode

Add this to your component for debugging:

```tsx
<OptimizedImage
  src={imageSrc}
  alt="Debug image"
  onError={() => console.log('Image failed to load:', imageSrc)}
  // ... other props
/>
```

## Future Enhancements

- [ ] Add image lazy loading intersection observer
- [ ] Implement progressive image loading
- [ ] Add image caching strategies
- [ ] Create image optimization analytics
- [ ] Add support for responsive image sets

## Dependencies

- `clsx`: Conditional class names
- `tailwind-merge`: Tailwind class deduplication
- `next/image`: Core Next.js image optimization

Install with:
```bash
npm install clsx tailwind-merge
```