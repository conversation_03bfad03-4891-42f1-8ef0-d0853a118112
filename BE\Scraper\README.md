# MetruyenScraper

A powerful and configurable web scraper specifically designed for Vietnamese novel websites, with primary support for webtruyen.diendantruyen.com. Built to handle dynamic content and anti-bot measures effectively.

## 🌟 Key Features

- **Dynamic Content Handling**: Uses Playwright browser automation to render JavaScript and load dynamic content
- **Anti-Detection System**: Implements user-agent rotation, intelligent delays, and stealth techniques to avoid detection
- **Vietnamese Text Support**: <PERSON>perly handles Vietnamese encoding and text processing
- **Robust Error Handling**: Automatic retry with exponential backoff for network and timeout errors
- **Multiple Export Formats**: Export scraped data to JSON, CSV, or Excel formats
- **Clean Architecture**: Well-documented, modular codebase with comprehensive error handling

---

## 🚀 Getting Started

### Prerequisites

- **Python 3.10, 3.11, or 3.12**. It is strongly recommended to use one of these versions to avoid installation issues. Python 3.13 and newer may cause errors when installing dependencies.
- **Git**

### Installation

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/MetruyenScraper.git
    cd MetruyenScraper
    ```

2.  **Create and activate a virtual environment:**
    This keeps your project dependencies isolated.
    ```bash
    # Replace 'python' with 'py -3.12' or your specific Python command if needed
    python -m venv venv
    ```
    -   **On Windows:**
        ```bash
        venv\Scripts\activate
        ```
    -   **On macOS/Linux:**
        ```bash
        source venv/bin/activate
        ```

3.  **Install required packages:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Install Playwright browsers:**
    This downloads the necessary browser binaries.
    ```bash
    playwright install chromium
    ```

### Quick Start

Here's a simple example to scrape a single chapter:

```python
import asyncio
from src.metruyenscraper import MetruyenScraper

async def main():
    # Use async context manager for proper resource management
    async with MetruyenScraper() as scraper:
        url = "https://webtruyen.diendantruyen.com/your-chapter-url"

        # Scrape the URL
        data = await scraper.scrape_url(url, "webtruyen")

        if data:
            print(f"Title: {data['title']}")
            print(f"Content length: {len(data['content'])} characters")
            print(f"Locked: {data['is_locked']}")

        # Export data to configured formats
        exported_files = scraper.export_data()
        print(f"Data exported to: {exported_files}")

if __name__ == "__main__":
    asyncio.run(main())
```

---

## ⚙️ Configuration (`config.yaml`)

The scraper's behavior is controlled by `config.yaml`. Here are some key settings:

<details>
<summary><strong>Browser Settings</strong></summary>

```yaml
scraper:
  browser:
    headless: true      # Set to false to see the browser UI
    timeout: 30000      # Page load timeout in milliseconds
    viewport:
      width: 1920
      height: 1080
```
</details>

<details>
<summary><strong>Anti-Detection Settings</strong></summary>

```yaml
scraper:
  stealth:
    user_agents:
      - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) ..."
    delays:
      min_delay: 2  # Minimum delay between requests (seconds)
      max_delay: 5  # Maximum delay
```
</details>

<details>
<summary><strong>Target Website Selectors</strong></summary>

You need to tell the scraper where to find the data on the page using CSS selectors.

```yaml
targets:
  webtruyen:
    base_url: "https://webtruyen.diendantruyen.com/"
    selectors:
      title: "h1, .title"                 # Selector for the chapter title
      content: ".content, .chapter-content" # Selector for the main content
      next_chapter: "a:contains('Chương sau')" # Selector for the "next chapter" link
```
</details>

<details>
<summary><strong>Command-Line Usage</strong></summary>

The `run_scraper.py` script provides a convenient command-line interface to perform common scraping tasks.

**Scrape a Single URL:**
```bash
python run_scraper.py single "https://webtruyen.diendantruyen.com/your-chapter-url"
```

**Scrape Multiple URLs:**
Create a `urls.txt` file with URLs (one per line), then:
```bash
python run_scraper.py multiple urls.txt --max-concurrent 2
```

**Scrape All Chapters of a Story (Interactive Mode):**
```bash
python run_scraper.py story "https://webtruyen.diendantruyen.com/your-story-url"
# This will display available chapters and ask you to select a range
```

**Scrape Specific Chapter Range (Non-Interactive):**
```bash
python run_scraper.py story "https://webtruyen.diendantruyen.com/your-story-url" --start-chapter 26 --end-chapter 35
```

**Scrape All Chapters with Limit:**
```bash
python run_scraper.py story "https://webtruyen.diendantruyen.com/your-story-url" --max-chapters 10
```

**Test Scraping Capability:**
```bash
python run_scraper.py test "https://webtruyen.diendantruyen.com/your-test-url"
```

</details>

---

## 📚 Advanced Usage

<details>
<summary><strong>Scraping Multiple URLs Concurrently</strong></summary>

```python
async def scrape_multiple():
    urls = [
        "https://webtruyen.diendantruyen.com/truyen/example/chuong-1",
        "https://webtruyen.diendantruyen.com/truyen/example/chuong-2",
    ]
    
    async with MetruyenScraper() as scraper:
        # 'max_concurrent' controls how many pages are scraped at the same time.
        results = await scraper.scrape_urls(urls, max_concurrent=2)
        print(f"Scraped {len(results)} URLs successfully")
```
</details>

<details>
<summary><strong>Scraping All Chapters of a Story</strong></summary>

```python
async def scrape_story():
    story_url = "https://webtruyen.diendantruyen.com/truyen/example-story"
    
    async with MetruyenScraper() as scraper:
        # Interactive mode - displays chapter list and asks user for range
        results = await scraper.scrape_story_chapters(story_url)
        
        # Non-interactive mode - scrape specific chapter range
        results = await scraper.scrape_story_chapters(
            story_url, 
            interactive=False, 
            start_chapter=26, 
            end_chapter=35
        )
        
        # Scrape all chapters without interaction
        results = await scraper.scrape_story_chapters(
            story_url, 
            interactive=False
        )
        
        # Limit chapters after range selection
        results = await scraper.scrape_story_chapters(
            story_url, 
            max_chapters=10
        )
        
        print(f"Scraped {len(results)} chapters")
```
</details>

<details>
<summary><strong>Error Handling and Statistics</strong></summary>

The scraper keeps track of successes and failures.

```python
async with MetruyenScraper() as scraper:
    # ... perform scraping ...
    
    stats = scraper.get_statistics()
    print(f"Total items scraped: {stats['data_statistics']['total_items']}")
    print(f"Total errors: {stats['error_statistics']['total_errors']}")
    
    # Get a list of URLs that failed to scrape
    failed_urls = stats['failed_urls']
    for failed in failed_urls:
        print(f"Failed: {failed['url']} - Reason: {failed['error_type']}")
```
</details>

---

## 🔧 Troubleshooting

- **Installation Errors**: Make sure you are using a compatible Python version (3.10-3.12). This is the most common issue.
- **Timeout Errors**: The website is slow to load. Increase the `timeout` value in `config.yaml`.
- **Blocked or CAPTCHA**: The website has detected the scraper. Try increasing the `min_delay` and `max_delay` values in `config.yaml`, or configure proxies.
- **Incorrect Data Scraped**: The CSS selectors in `config.yaml` are likely incorrect for your target website. You will need to inspect the website's HTML to find the correct ones.

---

## ⚖️ Legal

This project is for educational purposes only. Please respect the website's terms of service and `robots.txt`. Be responsible and do not overload website servers.

## 🤝 Contributing

Contributions are welcome! Please fork the repository and submit a pull request.
