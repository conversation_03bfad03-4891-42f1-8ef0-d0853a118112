import { Story, Chapter, StoriesResponse, ChaptersResponse, StoryWithChapters } from '@/types/story';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
const API_PREFIX = '/api/v1/stories';

// Fetch all stories with pagination
export const fetchStories = async (page: number = 1, pageSize: number = 20): Promise<StoriesResponse> => {
  const response = await fetch(`${API_BASE_URL}${API_PREFIX}?page=${page}&page_size=${pageSize}`);
  if (!response.ok) {
    throw new Error('Failed to fetch stories');
  }
  
  return response.json();
};

// Fetch a single story by ID
export const fetchStoryById = async (storyId: string): Promise<StoryWithChapters> => {
  const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch story');
  }
  
  return response.json();
};

// Fetch chapters for a story
export const fetchChaptersByStoryId = async (
  storyId: string,
  page: number = 1,
  pageSize: number = 50,
  filters?: {
    enhanced_only?: boolean;
    scraped_only?: boolean;
  }
): Promise<ChaptersResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString()
  });

  if (filters?.enhanced_only) {
    params.append('enhanced_only', 'true');
  }
  if (filters?.scraped_only) {
    params.append('scraped_only', 'true');
  }

  const response = await fetch(
    `${API_BASE_URL}${API_PREFIX}/${storyId}/chapters?${params.toString()}`
  );

  if (!response.ok) {
    throw new Error('Failed to fetch chapters');
  }

  return response.json();
};

// Fetch a single chapter by chapter number
export const fetchChapterByNumber = async (storyId: string, chapterNumber: number): Promise<Chapter> => {
  const response = await fetch(`${API_BASE_URL}${API_PREFIX}/${storyId}/chapters/${chapterNumber}`, {
    cache: 'no-store'
  });
  

  if (!response.ok) {
    throw new Error('Failed to fetch chapter');
  }
  
  const data = await response.json();
  // Backend returns ChapterContentResponse which extends APIResponse
  // Extract the chapter data from the response structure
  return {
    id: data.id,
    story_id: data.story_id,
    chapter_number: data.chapter_number,
    title: data.title,
    url: data.url,
    original_content: data.original_content,
    enhanced_content: data.enhanced_content,
    is_scraped: !!data.original_content,
    is_enhanced: data.is_enhanced,
    enhancement_status: data.enhancement_status,
    word_count: data.word_count,
    created_at: data.created_at,
    updated_at: data.updated_at,
    enhancement_metadata: data.enhancement_metadata
  };
};

// Fetch a single chapter by ID
export const fetchChapterById = async (chapterId: string): Promise<Chapter> => {
  const response = await fetch(`${API_BASE_URL}/api/v1/chapters/${chapterId}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch chapter');
  }
  
  return response.json();
};