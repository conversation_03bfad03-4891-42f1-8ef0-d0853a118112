# MongoDB Container Interaction Scripts

This directory contains two powerful scripts to help you interact with your MongoDB container for the Webtruyen project.

## 🚀 Quick Start

### 1. Interactive Python Script (`mongo_interactive_simple.py`)

A comprehensive Python-based interactive tool using Docker exec commands for all MongoDB operations.

**Features:**
- 🔌 Connection testing via Docker exec
- 📝 Complete CRUD operations using Docker exec
- 🗄️ Database and collection management
- 📊 Database statistics and collection info
- 🐳 Container management (start, stop, restart, logs)
- 📤 Data export/import capabilities
- 🔍 Advanced querying with JSON filters
- ✅ Input validation and error handling
- 🚫 No external dependencies (PyMongo not required)

**Usage:**
```bash
python mongo_interactive_simple.py
```

### 2. Shell Script (`mongo_quick.sh`)

A fast shell script for quick operations without needing to enter the interactive interface.

**Features:**
- 🐳 Container management
- 🗄️ Database operations
- 📋 Quick stats and collection listing
- 💾 Backup and restore functionality
- 🔧 MongoDB shell access

**Usage:**
```bash
./mongo_quick.sh [command] [options]
```

## 📋 Commands Reference

### Python Interactive Script Commands

1. **🔌 Connect to MongoDB** - Establish database connection
2. **📊 Show Database Info** - Display database and collection statistics
3. **📝 CRUD Operations** - Full Create, Read, Update, Delete operations
4. **🔧 Collection Management** - Manage collections (coming soon)
5. **📤 Export Data** - Export collection data to JSON
6. **📥 Import Data** - Import data from JSON (coming soon)
7. **🐳 Container Management** - Start, stop, restart, check status, view logs
8. **⚙️ Settings** - Configure connection settings (coming soon)
9. **🚪 Exit** - Close application

### Shell Script Commands

| Command | Description | Example |
|---------|-------------|---------|
| `status` | Check container status | `./mongo_quick.sh status` |
| `start` | Start MongoDB container | `./mongo_quick.sh start` |
| `stop` | Stop MongoDB container | `./mongo_quick.sh stop` |
| `restart` | Restart MongoDB container | `./mongo_quick.sh restart` |
| `logs [lines]` | Show container logs | `./mongo_quick.sh logs 100` |
| `shell` | Open MongoDB shell | `./mongo_quick.sh shell` |
| `collections` | List all collections | `./mongo_quick.sh collections` |
| `stats` | Show database statistics | `./mongo_quick.sh stats` |
| `backup [name]` | Backup database | `./mongo_quick.sh backup my_backup` |
| `restore <path>` | Restore from backup | `./mongo_quick.sh restore ./backups/my_backup` |
| `help` | Show help information | `./mongo_quick.sh help` |

## 🔧 Configuration

Both scripts are pre-configured for your Webtruyen project:

- **Container Name:** `webtruyen_mongodb`
- **Database:** `webtruyen_api`
- **Username:** `admin`
- **Password:** `password123`
- **Port:** `27017`
- **Auth Source:** `admin`

## 📦 Dependencies

### Python Script Requirements
**No external dependencies required!** Uses only Python standard library:
- `json`, `subprocess`, `tempfile`, `datetime`
- Works with Python 3.6+

### Shell Script Requirements
- Docker (must be running)
- Bash shell
- MongoDB container must be available

## 🌟 Examples

### Python Script Usage

1. **Start the interactive tool:**
```bash
python mongo_interactive_simple.py
```

2. **Connect to MongoDB:**
   - Choose option 1 from the main menu
   - The script will automatically connect using your container settings

3. **Create a document:**
   - Choose option 3 (CRUD Operations)
   - Choose option 1 (Create Document)
   - Enter collection name: `novels`
   - Enter JSON data: `{"title": "Test Novel", "author": "John Doe", "status": "active"}`

4. **Read documents:**
   - Choose option 3 (CRUD Operations)
   - Choose option 2 (Read Documents)
   - Enter collection name: `novels`
   - Leave query empty for all documents or enter: `{"status": "active"}`

### Shell Script Usage

1. **Check container status:**
```bash
./mongo_quick.sh status
```

2. **Start container if not running:**
```bash
./mongo_quick.sh start
```

3. **Open MongoDB shell:**
```bash
./mongo_quick.sh shell
```

4. **Create a backup:**
```bash
./mongo_quick.sh backup novels_backup_2024
```

5. **View database statistics:**
```bash
./mongo_quick.sh stats
```

6. **Show recent logs:**
```bash
./mongo_quick.sh logs 50
```

## 📊 CRUD Operations Examples

### Create Operations
```json
// Novel document
{
  "title": "The Great Adventure",
  "author": "Jane Smith",
  "genre": "Fantasy",
  "status": "active",
  "chapters": 25,
  "rating": 4.5
}

// User document
{
  "username": "reader123",
  "email": "<EMAIL>",
  "subscription": "premium",
  "joined_date": "2024-01-15"
}
```

### Query Examples
```json
// Find active novels
{"status": "active"}

// Find novels by author
{"author": "Jane Smith"}

// Find novels with high rating
{"rating": {"$gte": 4.0}}

// Find premium users
{"subscription": "premium"}
```

### Update Examples
```json
// Update novel status
{"status": "completed", "completion_date": "2024-07-10"}

// Update user subscription
{"subscription": "premium", "upgraded_date": "2024-07-10"}
```

## 🔒 Security Notes

- Scripts use the credentials defined in your `docker-compose.yml`
- Always validate input data before creating/updating documents
- Use backups before performing bulk operations
- The interactive script includes confirmation prompts for destructive operations

## 🐛 Troubleshooting

### Common Issues

1. **Container not running:**
   - Use `./mongo_quick.sh status` to check
   - Use `./mongo_quick.sh start` to start

2. **Connection failed:**
   - Ensure container is running
   - Check if port 27017 is available
   - Verify credentials in configuration

3. **Permission denied on shell script:**
   - Make sure script is executable: `chmod +x ./mongo_quick.sh`

4. **Python script issues:**
   - No external dependencies required
   - Ensure Python 3.6+ is installed
   - Check that Docker exec commands work: `docker exec -it webtruyen_mongodb mongosh --version`

### Debug Tips

1. **Check Docker logs:**
```bash
./mongo_quick.sh logs 100
```

2. **Test MongoDB connection:**
```bash
./mongo_quick.sh shell
```

3. **Verify container status:**
```bash
docker ps | grep webtruyen_mongodb
```

## 🎯 Best Practices

1. **Always backup before major operations**
2. **Test queries on small datasets first**
3. **Use proper JSON format for all operations**
4. **Monitor container resources during heavy operations**
5. **Keep backups organized with descriptive names**

## 📚 Additional Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [PyMongo Documentation](https://pymongo.readthedocs.io/)
- [Docker MongoDB Guide](https://hub.docker.com/_/mongo)

---

**Happy database managing! 🎉**

