'use client';

import { useState, useEffect } from 'react';
import { Chapter, Story } from '@/types/story';
import { useChaptersCache } from '@/hooks/useChaptersCache';
import ReaderView from './ReaderView';
import ChapterNavigation from './ChapterNavigation';
import Link from 'next/link';
import ChapterListModal from './ChapterListModal';

interface ChapterReaderWrapperProps {
  story: Story;
  chapter: Chapter;
  storyId: string;
}

const ChapterReaderWrapper = ({ story, chapter, storyId }: ChapterReaderWrapperProps) => {
  const [navigationChapters, setNavigationChapters] = useState<Chapter[]>([]);
  const { 
    chapters: allChapters, 
    loadChapters 
  } = useChaptersCache(storyId);

  // Load all chapters and filter for navigation
  useEffect(() => {
    const loadNavigation = async () => {
      try {
        if (allChapters.length === 0) {
          await loadChapters(1, 1000); // Load with large page size to get all chapters
        }
      } catch (err) {
        console.error('Failed to load chapters:', err);
      }
    };
    loadNavigation();
  }, [allChapters.length, loadChapters]);

  // Filter navigation chapters from all chapters
  useEffect(() => {
    if (allChapters.length > 0) {
      const startChapter = Math.max(1, chapter.chapter_number - 2);
      const endChapter = chapter.chapter_number + 2;
      const navChapters = allChapters
        .filter(ch => ch.chapter_number >= startChapter && ch.chapter_number <= endChapter)
        .sort((a, b) => a.chapter_number - b.chapter_number);
      setNavigationChapters(navChapters);
    }
  }, [allChapters, chapter.chapter_number]);


  const previousChapter = navigationChapters.find(ch => ch.chapter_number === chapter.chapter_number - 1) || null;
  const nextChapter = navigationChapters.find(ch => ch.chapter_number === chapter.chapter_number + 1) || null;

  // Use allChapters for full navigation when available (from modal)
  const chaptersForNavigation = allChapters.length > 0 ? allChapters : navigationChapters;

  return (
    <div className="min-h-screen bg-zinc-900">
      {/* Back to Story Button */}
      <div className="text-left px-4 pt-8">
        <Link
          href={`/stories/${storyId}`}
          className="inline-flex items-center px-3 py-1 text-sm bg-zinc-700 hover:bg-zinc-600 text-gray-300 hover:text-white rounded transition-colors"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Story
        </Link>
      </div>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Story Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">{story.title}</h1>
          <h2 className="text-xl text-zinc-300 mb-4">
            Chapter {chapter.chapter_number}: {chapter.title}
          </h2>
        </div>
        
        {/* Chapter Controls */}
        <div className="flex items-center justify-between mb-6">
          {/* Basic Navigation */}
          <div className="flex items-center gap-4">
            {/* Show navigation when we have at least basic nav chapters */}
            <ChapterNavigation
              storyId={storyId}
              previousChapter={previousChapter}
              nextChapter={nextChapter}
            />
          </div>
          {/* Chapter List Modal */}
          <ChapterListModal storyId={storyId} currentChapter={chapter} />
        </div>

        {/* Chapter Content */}
        <ReaderView chapter={chapter} />
      </div>
    </div>
  );
};

export default ChapterReaderWrapper;