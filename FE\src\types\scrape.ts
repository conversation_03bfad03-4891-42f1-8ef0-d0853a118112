// ============================================================================
// Hierarchical Scraping Types
// ============================================================================

export interface HierarchicalScrapeRequest {
  story_url: string;
  max_pages?: number;
  scrape_content?: boolean;
}

export interface HierarchicalScrapeResponse {
  success: boolean;
  story_id: string;
  title: string;
  total_pages: number;
  total_chapters: number;
  pages_scraped: number;
  chapters_created: number;
  message: string;
  data?: any;
}

export interface StoryHierarchy {
  story_id: string;
  title: string;
  url: string;
  total_pages: number;
  total_chapters_scraped: number;
  metadata: Record<string, any>;
  pages: PageInfo[];
  created_at: string;
  updated_at: string;
}

export interface PageInfo {
  page_id: string;
  page_number: number;
  page_url: string;
  total_chapters_on_page: number;
  is_scraped: boolean;
  created_at: string;
  chapters?: ChapterInfo[];
}

export interface ChapterInfo {
  chapter_id: string;
  chapter_number: number;
  title: string;
  url: string;
  is_scraped: boolean;
  is_enhanced: boolean;
  word_count?: number;
  created_at: string;
}

export interface ChapterContent {
  chapter_id: string;
  title: string;
  content: string;
  word_count?: number;
  scraped_at: string;
}

export interface ScrapingStats {
  totals: {
    stories: number;
    pages: number;
    chapters: number;
  };
  progress: {
    pages_scraped: number;
    chapters_scraped: number;
    chapters_enhanced: number;
    page_scraping_rate: number;
    chapter_scraping_rate: number;
    enhancement_rate: number;
  };
  recent_stories: {
    story_id: string;
    title: string;
    total_pages: number;
    created_at: string;
  }[];
}

export interface BatchScrapingResult {
  success: boolean;
  message: string;
  scraped_count: number;
  failed_count: number;
  total_chapters: number;
}

// Legacy types for backward compatibility
export interface ScrapeRequest {
  url: string;
}

export interface ScrapeResponse {
  message: string;
  storyId?: string;
}

// Progress tracking types
export interface ScrapingProgress {
  phase: 'story_info' | 'pages' | 'chapters' | 'content' | 'completed' | 'error';
  current_step: number;
  total_steps: number;
  message: string;
  data?: any;
}

export interface ScrapingJob {
  id: string;
  story_url: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: ScrapingProgress;
  created_at: string;
  updated_at: string;
  result?: HierarchicalScrapeResponse;
  error?: string;
}