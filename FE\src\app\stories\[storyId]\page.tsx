import { fetchStoryById } from '@/services/storyService';
import StoryInfo from '@/components/story/StoryInfo';
import EnhancedChapterList from '@/components/story/EnhancedChapterList';
import { notFound } from 'next/navigation';
import { Story, Chapter, PaginatedAPIResponse } from '@/types/story';

interface StoryPageProps {
  params: {
    storyId: string;
  };
}

export default async function StoryPage({ params }: StoryPageProps) {
  try {
    // Only fetch story info on server-side, chapters will be loaded client-side
    const story = await fetchStoryById(params.storyId);

    return (
      <div className="container mx-auto px-4 py-8">
        <StoryInfo story={story} />
        <EnhancedChapterList
          storyId={params.storyId}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching story data:', error);
    notFound();
  }
}

export async function generateMetadata({ params }: StoryPageProps) {
  try {
    const story = await fetchStoryById(params.storyId);
    return {
      title: `${story.title} - WebTruyen`,
      description: story.description || `Đọc truyện ${story.title} của tác giả ${story.author}`,
    };
  } catch {
    return {
      title: 'Story Not Found - WebTruyen',
    };
  }
}