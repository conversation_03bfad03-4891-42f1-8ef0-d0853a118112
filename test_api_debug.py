import requests
import json

def test_api_with_debug():
    """Test API with detailed debugging"""
    try:
        print("🔍 Testing API with debug info...")
        
        # First test the scraper endpoint
        print("\n1. Testing scraper endpoint...")
        response = requests.get("http://localhost:8002/api/v1/tasks/scraping/test-scraping")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # Now test the actual story info endpoint
        print("\n2. Testing story info endpoint...")
        
        url = "https://webtruyen.diendantruyen.com/truyen/90-nha-ta-co-cai-tieu-tai-than/"
        payload = {
            "story_url": url,
            "max_pages": 5
        }
        
        response = requests.post(
            "http://localhost:8002/api/v1/tasks/scraping/story-info",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code != 200:
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
                
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_with_debug()