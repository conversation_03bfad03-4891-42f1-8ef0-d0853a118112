"""
Configuration Management for Vietnamese Web API

This module handles application configuration using Pydantic Settings
with support for environment variables and configuration files.
"""

import os
from functools import lru_cache
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # ========================================================================
    # Application Settings
    # ========================================================================
    
    app_name: str = "Web API"
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    version: str = "1.0.0"
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # ========================================================================
    # Database Settings
    # ========================================================================
    
    mongodb_url: str = Field(
        default="mongodb://localhost:27017",
        env="MONGODB_URL"
    )
    mongodb_database: str = Field(
        default="webtruyen_api",
        env="MONGODB_DATABASE"
    )
    
    # ========================================================================
    # Security Settings
    # ========================================================================
    
    allowed_origins: str = Field(
        default="http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:8080",
        env="ALLOWED_ORIGINS"
    )
    allowed_hosts: str = Field(
        default="localhost,127.0.0.1",
        env="ALLOWED_HOSTS"
    )
    
    # ========================================================================
    # Logging Settings
    # ========================================================================
    
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="api.log", env="LOG_FILE")
    log_format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}",
        env="LOG_FORMAT"
    )
    
    # ========================================================================
    # Scraping Settings
    # ========================================================================
    
    # Rate limiting
    scraping_rate_limit: int = Field(default=10, env="SCRAPING_RATE_LIMIT")  # requests per minute
    scraping_burst_limit: int = Field(default=5, env="SCRAPING_BURST_LIMIT")  # burst requests
    
    # Concurrency limits
    max_concurrent_scraping: int = Field(default=3, env="MAX_CONCURRENT_SCRAPING")
    max_concurrent_enhancement: int = Field(default=1, env="MAX_CONCURRENT_ENHANCEMENT")
    
    # Timeouts and delays
    scraping_timeout: int = Field(default=30, env="SCRAPING_TIMEOUT")  # seconds
    scraping_delay_min: float = Field(default=1.0, env="SCRAPING_DELAY_MIN")  # seconds
    scraping_delay_max: float = Field(default=3.0, env="SCRAPING_DELAY_MAX")  # seconds
    
    # Browser settings
    browser_headless: bool = Field(default=True, env="BROWSER_HEADLESS")
    browser_timeout: int = Field(default=30000, env="BROWSER_TIMEOUT")  # milliseconds
    
    # ========================================================================
    # AI Enhancement Settings
    # ========================================================================
    
    google_ai_api_key: Optional[str] = Field(default="", env="GOOGLE_AI_API_KEY")
    ai_model_name: str = Field(default="gemini-2.5-flash", env="AI_MODEL_NAME")
    ai_rate_limit_delay: float = Field(default=0.5, env="AI_RATE_LIMIT_DELAY")  # seconds
    ai_max_retries: int = Field(default=3, env="AI_MAX_RETRIES")
    ai_max_content_length: int = Field(default=6000, env="AI_MAX_CONTENT_LENGTH")  # characters
    ai_chunk_overlap: int = Field(default=200, env="AI_CHUNK_OVERLAP")  # characters
    
    # ========================================================================
    # Cache Settings
    # ========================================================================
    
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")  # seconds
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")  # entries
    
    # ========================================================================
    # File Storage Settings
    # ========================================================================
    
    storage_path: str = Field(default="./storage", env="STORAGE_PATH")
    export_path: str = Field(default="./exports", env="EXPORT_PATH")
    temp_path: str = Field(default="./temp", env="TEMP_PATH")
    
    # File size limits (in MB)
    max_export_file_size: int = Field(default=100, env="MAX_EXPORT_FILE_SIZE")
    max_upload_file_size: int = Field(default=10, env="MAX_UPLOAD_FILE_SIZE")
    
    # ========================================================================
    # Job Management Settings
    # ========================================================================
    
    job_cleanup_interval: int = Field(default=3600, env="JOB_CLEANUP_INTERVAL")  # seconds
    job_retention_days: int = Field(default=7, env="JOB_RETENTION_DAYS")
    max_concurrent_jobs: int = Field(default=5, env="MAX_CONCURRENT_JOBS")
    
    # ========================================================================
    # Monitoring and Metrics Settings
    # ========================================================================
    
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")  # seconds
    
    # ========================================================================
    # Validators
    # ========================================================================
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_environments = ['development', 'staging', 'production']
        if v not in valid_environments:
            raise ValueError(f'Environment must be one of: {valid_environments}')
        return v
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    

    
    @validator('scraping_delay_min', 'scraping_delay_max')
    def validate_delays(cls, v):
        if v < 0:
            raise ValueError('Delay values must be non-negative')
        return v
    
    @validator('scraping_delay_max')
    def validate_delay_range(cls, v, values):
        min_delay = values.get('scraping_delay_min', 0)
        if v < min_delay:
            raise ValueError('Max delay must be greater than or equal to min delay')
        return v
    
    @validator('port')
    def validate_port(cls, v):
        if not (1 <= v <= 65535):
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    # ========================================================================
    # Configuration Properties
    # ========================================================================
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode"""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return self.environment == "production"

    @property
    def database_url(self) -> str:
        """Get complete database URL"""
        return f"{self.mongodb_url}/{self.mongodb_database}"

    @property
    def allowed_origins_list(self) -> List[str]:
        """Get allowed origins as list"""
        return [origin.strip() for origin in self.allowed_origins.split(',') if origin.strip()]

    @property
    def allowed_hosts_list(self) -> List[str]:
        """Get allowed hosts as list"""
        return [host.strip() for host in self.allowed_hosts.split(',') if host.strip()]
    
    def get_scraper_config(self) -> dict:
        """Get scraper configuration dictionary"""
        return {
            'browser': {
                'headless': self.browser_headless,
                'timeout': self.browser_timeout
            },
            'stealth': {
                'delays': {
                    'min_delay': self.scraping_delay_min,
                    'max_delay': self.scraping_delay_max
                }
            },
            'retry': {
                'max_attempts': 3,
                'timeout': self.scraping_timeout,
                'backoff_factor': 2
            }
        }
    
    def get_ai_config(self) -> dict:
        """Get AI enhancement configuration dictionary"""
        return {
            'api_key': self.google_ai_api_key,
            'model_name': self.ai_model_name,
            'rate_limit_delay': self.ai_rate_limit_delay,
            'max_retries': self.ai_max_retries,
            'max_content_length': self.ai_max_content_length,
            'chunk_overlap': self.ai_chunk_overlap
        }
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


# ============================================================================
# Configuration Utilities
# ============================================================================

def create_directories():
    """Create necessary directories for the application"""
    settings = get_settings()
    
    directories = [
        settings.storage_path,
        settings.export_path,
        settings.temp_path,
        os.path.dirname(settings.log_file) if os.path.dirname(settings.log_file) else "."
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


def validate_configuration():
    """Validate configuration and check required dependencies"""
    settings = get_settings()
    
    # Check required environment variables for production
    if settings.is_production:
        required_vars = ['GOOGLE_AI_API_KEY', 'MONGODB_URL']
        missing_vars = [var for var in required_vars if not getattr(settings, var.lower())]
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {missing_vars}")
    
    # Create necessary directories
    create_directories()
    
    return True
